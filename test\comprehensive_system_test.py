#!/usr/bin/env python3
"""
Comprehensive system test for Agno WorkSphere
Tests all endpoints, functionality, and database persistence across all user roles
"""
import requests
import json
import time
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

class SystemTester:
    def __init__(self):
        self.base_url = "http://localhost:3001/api/v1"
        self.frontend_url = "http://localhost:3000"
        self.test_results = []
        self.auth_tokens = {}
        self.test_data = {}
        
    def log_test(self, test_name: str, status: str, details: str = ""):
        """Log test results"""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_emoji} {test_name}: {status}")
        if details:
            print(f"   Details: {details}")
    
    def make_request(self, method: str, endpoint: str, data: Dict = None, 
                    headers: Dict = None, params: Dict = None) -> requests.Response:
        """Make HTTP request with error handling"""
        url = f"{self.base_url}{endpoint}"
        default_headers = {"Content-Type": "application/json"}
        if headers:
            default_headers.update(headers)
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=default_headers, params=params)
            elif method.upper() == "POST":
                response = requests.post(url, headers=default_headers, json=data, params=params)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=default_headers, json=data, params=params)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=default_headers, params=params)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            return response
        except Exception as e:
            print(f"Request failed: {e}")
            raise
    
    def test_api_root(self):
        """Test API root endpoint"""
        try:
            response = self.make_request("GET", "/")
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and "Agno WorkSphere API" in data.get("data", {}).get("message", ""):
                    self.log_test("API Root Endpoint", "PASS", f"API version: {data['data'].get('version')}")
                else:
                    self.log_test("API Root Endpoint", "FAIL", "Invalid response format")
            else:
                self.log_test("API Root Endpoint", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("API Root Endpoint", "FAIL", str(e))
    
    def test_user_registration_and_authentication(self):
        """Test user registration and authentication for all roles"""
        test_users = [
            {"email": "<EMAIL>", "password": "testpass123", "role": "owner", "name": "Test Owner"},
            {"email": "<EMAIL>", "password": "testpass123", "role": "admin", "name": "Test Admin"},
            {"email": "<EMAIL>", "password": "testpass123", "role": "member", "name": "Test Member"},
            {"email": "<EMAIL>", "password": "testpass123", "role": "viewer", "name": "Test Viewer"}
        ]
        
        for user in test_users:
            try:
                # Test registration
                reg_data = {
                    "email": user["email"],
                    "password": user["password"],
                    "full_name": user["name"]
                }
                response = self.make_request("POST", "/auth/register", reg_data)
                
                if response.status_code in [200, 201, 409]:  # 409 if user already exists
                    # Test login
                    login_data = {
                        "username": user["email"],
                        "password": user["password"]
                    }
                    login_response = self.make_request("POST", "/auth/login", login_data)
                    
                    if login_response.status_code == 200:
                        token_data = login_response.json()
                        if "access_token" in token_data:
                            self.auth_tokens[user["role"]] = token_data["access_token"]
                            self.log_test(f"Authentication - {user['role']}", "PASS", 
                                        f"Token obtained for {user['email']}")
                        else:
                            self.log_test(f"Authentication - {user['role']}", "FAIL", 
                                        "No access token in response")
                    else:
                        self.log_test(f"Authentication - {user['role']}", "FAIL", 
                                    f"Login failed: {login_response.status_code}")
                else:
                    self.log_test(f"Registration - {user['role']}", "FAIL", 
                                f"Registration failed: {response.status_code}")
            except Exception as e:
                self.log_test(f"Authentication - {user['role']}", "FAIL", str(e))
    
    def test_organization_management(self):
        """Test organization creation and management"""
        if "owner" not in self.auth_tokens:
            self.log_test("Organization Management", "SKIP", "No owner token available")
            return
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_tokens['owner']}"}
            
            # Test organization creation
            org_data = {
                "name": "Test Organization",
                "description": "Test organization for system testing",
                "industry": "Technology",
                "size": "small",
                "website": "https://test.com",
                "contact_email": "<EMAIL>",
                "timezone": "UTC"
            }
            
            response = self.make_request("POST", "/organizations", org_data, headers)
            
            if response.status_code in [200, 201]:
                org_response = response.json()
                self.test_data["organization_id"] = org_response.get("id")
                self.log_test("Organization Creation", "PASS", 
                            f"Organization ID: {self.test_data['organization_id']}")
                
                # Test organization retrieval
                get_response = self.make_request("GET", f"/organizations/{self.test_data['organization_id']}", 
                                               headers=headers)
                if get_response.status_code == 200:
                    self.log_test("Organization Retrieval", "PASS")
                else:
                    self.log_test("Organization Retrieval", "FAIL", 
                                f"Status: {get_response.status_code}")
            else:
                self.log_test("Organization Creation", "FAIL", 
                            f"Status: {response.status_code}, Response: {response.text}")
        except Exception as e:
            self.log_test("Organization Management", "FAIL", str(e))
    
    def test_project_management(self):
        """Test project creation and management"""
        if "owner" not in self.auth_tokens or "organization_id" not in self.test_data:
            self.log_test("Project Management", "SKIP", "Prerequisites not met")
            return
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_tokens['owner']}"}
            
            # Test project creation
            project_data = {
                "name": "Test Project",
                "description": "Test project for system testing",
                "status": "active",
                "priority": "high",
                "start_date": datetime.now().date().isoformat(),
                "due_date": (datetime.now() + timedelta(days=30)).date().isoformat()
            }
            
            params = {"organization_id": self.test_data["organization_id"]}
            response = self.make_request("POST", "/projects", project_data, headers, params)
            
            if response.status_code in [200, 201]:
                project_response = response.json()
                self.test_data["project_id"] = project_response.get("id")
                self.log_test("Project Creation", "PASS", 
                            f"Project ID: {self.test_data['project_id']}")
                
                # Test project retrieval
                get_response = self.make_request("GET", f"/projects/{self.test_data['project_id']}", 
                                               headers=headers)
                if get_response.status_code == 200:
                    self.log_test("Project Retrieval", "PASS")
                else:
                    self.log_test("Project Retrieval", "FAIL", 
                                f"Status: {get_response.status_code}")
            else:
                self.log_test("Project Creation", "FAIL", 
                            f"Status: {response.status_code}, Response: {response.text}")
        except Exception as e:
            self.log_test("Project Management", "FAIL", str(e))
    
    def test_board_and_column_management(self):
        """Test board and column creation and management"""
        if "owner" not in self.auth_tokens or "project_id" not in self.test_data:
            self.log_test("Board Management", "SKIP", "Prerequisites not met")
            return
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_tokens['owner']}"}
            
            # Test board creation
            board_data = {
                "name": "Test Board",
                "description": "Test board for system testing"
            }
            
            params = {"project_id": self.test_data["project_id"]}
            response = self.make_request("POST", "/boards", board_data, headers, params)
            
            if response.status_code in [200, 201]:
                board_response = response.json()
                self.test_data["board_id"] = board_response.get("id")
                self.log_test("Board Creation", "PASS", 
                            f"Board ID: {self.test_data['board_id']}")
                
                # Test default columns creation
                default_columns = ["To-Do", "In Progress", "Review", "Done"]
                for i, col_name in enumerate(default_columns):
                    col_data = {
                        "name": col_name,
                        "position": i,
                        "color": "#3b82f6"
                    }
                    col_params = {"board_id": self.test_data["board_id"]}
                    col_response = self.make_request("POST", "/columns", col_data, headers, col_params)
                    
                    if col_response.status_code in [200, 201]:
                        if i == 0:  # Store first column for card testing
                            col_data_response = col_response.json()
                            self.test_data["column_id"] = col_data_response.get("id")
                        self.log_test(f"Column Creation - {col_name}", "PASS")
                    else:
                        self.log_test(f"Column Creation - {col_name}", "FAIL", 
                                    f"Status: {col_response.status_code}")
            else:
                self.log_test("Board Creation", "FAIL", 
                            f"Status: {response.status_code}, Response: {response.text}")
        except Exception as e:
            self.log_test("Board Management", "FAIL", str(e))
    
    def test_card_management(self):
        """Test card creation, editing, and management"""
        if "owner" not in self.auth_tokens or "column_id" not in self.test_data:
            self.log_test("Card Management", "SKIP", "Prerequisites not met")
            return

        try:
            headers = {"Authorization": f"Bearer {self.auth_tokens['owner']}"}

            # Test card creation
            card_data = {
                "title": "Test Card",
                "description": "Test card for system testing",
                "column_id": self.test_data["column_id"],
                "position": 0,
                "priority": "high",
                "due_date": (datetime.now() + timedelta(days=7)).isoformat()
            }

            response = self.make_request("POST", "/cards", card_data, headers)

            if response.status_code in [200, 201]:
                card_response = response.json()
                self.test_data["card_id"] = card_response.get("id")
                self.log_test("Card Creation", "PASS",
                            f"Card ID: {self.test_data['card_id']}")

                # Test card retrieval
                get_response = self.make_request("GET", f"/cards/{self.test_data['card_id']}",
                                               headers=headers)
                if get_response.status_code == 200:
                    self.log_test("Card Retrieval", "PASS")
                else:
                    self.log_test("Card Retrieval", "FAIL",
                                f"Status: {get_response.status_code}")

                # Test card update
                update_data = {
                    "title": "Updated Test Card",
                    "description": "Updated description"
                }
                update_response = self.make_request("PUT", f"/cards/{self.test_data['card_id']}",
                                                  update_data, headers)
                if update_response.status_code == 200:
                    self.log_test("Card Update", "PASS")
                else:
                    self.log_test("Card Update", "FAIL",
                                f"Status: {update_response.status_code}")
            else:
                self.log_test("Card Creation", "FAIL",
                            f"Status: {response.status_code}, Response: {response.text}")
        except Exception as e:
            self.log_test("Card Management", "FAIL", str(e))

    def test_checklist_functionality(self):
        """Test checklist creation and management"""
        if "owner" not in self.auth_tokens or "card_id" not in self.test_data:
            self.log_test("Checklist Functionality", "SKIP", "Prerequisites not met")
            return

        try:
            headers = {"Authorization": f"Bearer {self.auth_tokens['owner']}"}

            # Test checklist item creation
            checklist_data = {
                "text": "Test checklist item",
                "position": 0
            }

            params = {"card_id": self.test_data["card_id"]}
            response = self.make_request("POST", "/checklist/items", checklist_data, headers, params)

            if response.status_code in [200, 201]:
                checklist_response = response.json()
                checklist_id = checklist_response.get("id")
                self.log_test("Checklist Item Creation", "PASS",
                            f"Checklist ID: {checklist_id}")

                # Test checklist item completion
                complete_data = {"completed": True}
                complete_response = self.make_request("PUT", f"/checklist/items/{checklist_id}",
                                                    complete_data, headers)
                if complete_response.status_code == 200:
                    self.log_test("Checklist Item Completion", "PASS")
                else:
                    self.log_test("Checklist Item Completion", "FAIL",
                                f"Status: {complete_response.status_code}")

                # Test AI checklist generation (owner only)
                ai_data = {
                    "task_title": "Test AI Task",
                    "task_description": "Generate checklist for testing",
                    "project_type": "software"
                }
                ai_response = self.make_request("POST", "/checklist/ai-generate", ai_data, headers)
                if ai_response.status_code == 200:
                    self.log_test("AI Checklist Generation", "PASS")
                else:
                    self.log_test("AI Checklist Generation", "FAIL",
                                f"Status: {ai_response.status_code}")
            else:
                self.log_test("Checklist Item Creation", "FAIL",
                            f"Status: {response.status_code}, Response: {response.text}")
        except Exception as e:
            self.log_test("Checklist Functionality", "FAIL", str(e))

    def test_role_based_access_control(self):
        """Test role-based access control across different user roles"""
        roles_to_test = ["viewer", "member", "admin", "owner"]

        for role in roles_to_test:
            if role not in self.auth_tokens:
                self.log_test(f"RBAC - {role}", "SKIP", "Token not available")
                continue

            try:
                headers = {"Authorization": f"Bearer {self.auth_tokens[role]}"}

                # Test user profile access (all roles should have this)
                profile_response = self.make_request("GET", "/users/profile", headers=headers)
                if profile_response.status_code == 200:
                    self.log_test(f"RBAC Profile Access - {role}", "PASS")
                else:
                    self.log_test(f"RBAC Profile Access - {role}", "FAIL",
                                f"Status: {profile_response.status_code}")

                # Test organization creation (owner only)
                if role == "owner":
                    org_data = {"name": f"Test Org {role}", "description": "Test"}
                    org_response = self.make_request("POST", "/organizations", org_data, headers)
                    expected_status = [200, 201, 409]  # 409 if already exists
                else:
                    org_data = {"name": f"Test Org {role}", "description": "Test"}
                    org_response = self.make_request("POST", "/organizations", org_data, headers)
                    expected_status = [403, 401]  # Should be forbidden

                if org_response.status_code in expected_status:
                    self.log_test(f"RBAC Organization Creation - {role}", "PASS")
                else:
                    self.log_test(f"RBAC Organization Creation - {role}", "FAIL",
                                f"Status: {org_response.status_code}, Expected: {expected_status}")

                # Test AI features (owner only)
                if "card_id" in self.test_data:
                    ai_data = {"task_title": "Test", "task_description": "Test", "project_type": "software"}
                    ai_response = self.make_request("POST", "/checklist/ai-generate", ai_data, headers)

                    if role == "owner":
                        expected_ai_status = [200, 201]
                    else:
                        expected_ai_status = [403, 401]

                    if ai_response.status_code in expected_ai_status:
                        self.log_test(f"RBAC AI Features - {role}", "PASS")
                    else:
                        self.log_test(f"RBAC AI Features - {role}", "FAIL",
                                    f"Status: {ai_response.status_code}, Expected: {expected_ai_status}")

            except Exception as e:
                self.log_test(f"RBAC - {role}", "FAIL", str(e))

    def test_database_persistence(self):
        """Test that data is properly persisted in database"""
        if "owner" not in self.auth_tokens:
            self.log_test("Database Persistence", "SKIP", "No owner token available")
            return

        try:
            headers = {"Authorization": f"Bearer {self.auth_tokens['owner']}"}

            # Test data persistence by retrieving created entities
            entities_to_check = [
                ("organization", "organization_id", "/organizations"),
                ("project", "project_id", "/projects"),
                ("board", "board_id", "/boards"),
                ("card", "card_id", "/cards")
            ]

            for entity_name, id_key, endpoint in entities_to_check:
                if id_key in self.test_data:
                    entity_id = self.test_data[id_key]
                    response = self.make_request("GET", f"{endpoint}/{entity_id}", headers=headers)

                    if response.status_code == 200:
                        data = response.json()
                        if data.get("id") == entity_id:
                            self.log_test(f"DB Persistence - {entity_name}", "PASS",
                                        f"ID: {entity_id}")
                        else:
                            self.log_test(f"DB Persistence - {entity_name}", "FAIL",
                                        "ID mismatch in response")
                    else:
                        self.log_test(f"DB Persistence - {entity_name}", "FAIL",
                                    f"Status: {response.status_code}")
                else:
                    self.log_test(f"DB Persistence - {entity_name}", "SKIP",
                                "Entity not created in tests")
        except Exception as e:
            self.log_test("Database Persistence", "FAIL", str(e))

    def run_all_tests(self):
        """Run all system tests"""
        print("🚀 Starting Comprehensive System Test for Agno WorkSphere")
        print("=" * 60)

        # Basic API tests
        self.test_api_root()

        # Authentication tests
        self.test_user_registration_and_authentication()

        # Organization management tests
        self.test_organization_management()

        # Project management tests
        self.test_project_management()

        # Board and column management tests
        self.test_board_and_column_management()

        # Card management tests
        self.test_card_management()

        # Checklist functionality tests
        self.test_checklist_functionality()

        # Role-based access control tests
        self.test_role_based_access_control()

        # Database persistence tests
        self.test_database_persistence()

        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        passed = len([r for r in self.test_results if r["status"] == "PASS"])
        failed = len([r for r in self.test_results if r["status"] == "FAIL"])
        skipped = len([r for r in self.test_results if r["status"] == "SKIP"])
        
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"⚠️  Skipped: {skipped}")
        print(f"📈 Total: {len(self.test_results)}")
        
        if failed > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if result["status"] == "FAIL":
                    print(f"   - {result['test']}: {result['details']}")

if __name__ == "__main__":
    tester = SystemTester()
    tester.run_all_tests()
