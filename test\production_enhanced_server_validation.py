#!/usr/bin/env python3
"""
Production Enhanced Server Validation Test
Comprehensive testing of the production-ready enhanced server
"""
import requests
import json
import time
import sys
import os
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:3001"
TEST_USER_EMAIL = f"test_user_{int(time.time())}@testcompany.com"
TEST_PASSWORD = "TestPassword123!"

class ProductionServerValidator:
    """Validates production-ready enhanced server functionality"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.test_results = []
        self.auth_token = None
        
    def log_test(self, test_name, success, details="", response_data=None):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        if response_data:
            result["response_data"] = response_data
            
        self.test_results.append(result)
        print(f"{status} {test_name}")
        if details:
            print(f"    📝 {details}")
        if response_data and not success:
            print(f"    📊 Response: {response_data}")
    
    def test_server_health(self):
        """Test server health endpoint"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check required fields
                required_fields = ["status", "timestamp", "database", "cache", "version"]
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    self.log_test("Server Health Check", False, 
                                f"Missing fields: {missing_fields}", data)
                    return False
                
                # Check database status
                db_healthy = data.get("database") == "connected"
                cache_status = data.get("cache", "disabled")
                
                details = f"DB: {data.get('database')}, Cache: {cache_status}, Version: {data.get('version')}"
                self.log_test("Server Health Check", db_healthy, details, data)
                return db_healthy
            else:
                self.log_test("Server Health Check", False, 
                            f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test("Server Health Check", False, str(e))
            return False
    
    def test_detailed_health(self):
        """Test detailed health endpoint"""
        try:
            response = requests.get(f"{self.base_url}/health/detailed", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check components
                components = data.get("components", {})
                db_status = components.get("database", {}).get("status")
                cache_status = components.get("cache", {}).get("status")
                
                success = db_status == "healthy"
                details = f"Overall: {data.get('overall_status')}, DB: {db_status}, Cache: {cache_status}"
                
                self.log_test("Detailed Health Check", success, details)
                return success
            else:
                self.log_test("Detailed Health Check", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Detailed Health Check", False, str(e))
            return False
    
    def test_user_registration(self):
        """Test user registration functionality"""
        try:
            user_data = {
                "email": TEST_USER_EMAIL,
                "password": TEST_PASSWORD,
                "first_name": "Test",
                "last_name": "User",
                "organization_name": "Test Organization"
            }
            
            response = requests.post(f"{self.base_url}/api/auth/register", 
                                   json=user_data, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                success = data.get("success", False)
                user_id = data.get("user_id")
                
                details = f"User ID: {user_id}, Email: {data.get('email')}"
                self.log_test("User Registration", success, details)
                return success
            else:
                self.log_test("User Registration", False, 
                            f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test("User Registration", False, str(e))
            return False
    
    def test_user_login(self):
        """Test user login functionality"""
        try:
            login_data = {
                "email": TEST_USER_EMAIL,
                "password": TEST_PASSWORD
            }
            
            response = requests.post(f"{self.base_url}/api/auth/login", 
                                   json=login_data, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                success = data.get("success", False)
                self.auth_token = data.get("access_token")
                
                details = f"Token received: {bool(self.auth_token)}, User: {data.get('user', {}).get('email')}"
                self.log_test("User Login", success, details)
                return success
            else:
                self.log_test("User Login", False, 
                            f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test("User Login", False, str(e))
            return False
    
    def test_organizations_api(self):
        """Test organizations API"""
        try:
            response = requests.get(f"{self.base_url}/api/organizations", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                org_count = len(data) if isinstance(data, list) else 0
                
                details = f"Organizations retrieved: {org_count}"
                self.log_test("Organizations API", True, details)
                return True
            else:
                self.log_test("Organizations API", False, 
                            f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test("Organizations API", False, str(e))
            return False
    
    def test_projects_api(self):
        """Test projects API"""
        try:
            response = requests.get(f"{self.base_url}/api/projects", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                project_count = len(data) if isinstance(data, list) else 0
                
                details = f"Projects retrieved: {project_count}"
                self.log_test("Projects API", True, details)
                return True
            else:
                self.log_test("Projects API", False, 
                            f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test("Projects API", False, str(e))
            return False
    
    def test_ai_projects_api(self):
        """Test AI projects API"""
        try:
            response = requests.get(f"{self.base_url}/api/ai-projects", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                ai_project_count = len(data) if isinstance(data, list) else 0
                
                details = f"AI projects retrieved: {ai_project_count}"
                self.log_test("AI Projects API", True, details)
                return True
            else:
                self.log_test("AI Projects API", False, 
                            f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test("AI Projects API", False, str(e))
            return False
    
    def test_metrics_endpoint(self):
        """Test metrics endpoint"""
        try:
            response = requests.get(f"{self.base_url}/metrics", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check required metrics
                required_metrics = ["database", "pool_stats", "cache_stats", "timestamp"]
                missing_metrics = [metric for metric in required_metrics if metric not in data]
                
                if missing_metrics:
                    self.log_test("Metrics Endpoint", False, 
                                f"Missing metrics: {missing_metrics}")
                    return False
                
                db_metrics = data.get("database", {})
                user_count = db_metrics.get("user_count", 0)
                
                details = f"Users: {user_count}, Pool queries: {data.get('pool_stats', {}).get('queries_executed', 0)}"
                self.log_test("Metrics Endpoint", True, details)
                return True
            else:
                self.log_test("Metrics Endpoint", False, 
                            f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test("Metrics Endpoint", False, str(e))
            return False
    
    def test_authenticated_endpoint(self):
        """Test authenticated endpoint"""
        if not self.auth_token:
            self.log_test("Authenticated Endpoint", False, "No auth token available")
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            response = requests.get(f"{self.base_url}/api/users/me", 
                                  headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                user_email = data.get("email")
                
                details = f"User profile retrieved: {user_email}"
                self.log_test("Authenticated Endpoint", True, details)
                return True
            else:
                self.log_test("Authenticated Endpoint", False, 
                            f"HTTP {response.status_code}", response.text)
                return False
                
        except Exception as e:
            self.log_test("Authenticated Endpoint", False, str(e))
            return False
    
    def run_all_tests(self):
        """Run all production validation tests"""
        print("🚀 Production Enhanced Server Validation")
        print("=" * 70)
        print(f"📍 Server URL: {self.base_url}")
        print(f"🕒 Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        tests = [
            self.test_server_health,
            self.test_detailed_health,
            self.test_user_registration,
            self.test_user_login,
            self.test_organizations_api,
            self.test_projects_api,
            self.test_ai_projects_api,
            self.test_metrics_endpoint,
            self.test_authenticated_endpoint
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
            time.sleep(0.5)  # Brief pause between tests
        
        print()
        print("📊 Production Validation Summary")
        print("=" * 70)
        print(f"✅ Passed: {passed}/{total}")
        print(f"❌ Failed: {total - passed}/{total}")
        print(f"📈 Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 ALL PRODUCTION VALIDATION TESTS PASSED!")
            print("✅ Enhanced server is PRODUCTION-READY!")
            print("🚀 Ready for deployment with:")
            print("   • Database integration with connection pooling")
            print("   • Redis caching with graceful fallback")
            print("   • Comprehensive health monitoring")
            print("   • AI projects table and API endpoints")
            print("   • Production-grade error handling")
        else:
            print(f"\n⚠️ {total - passed} tests failed. Review issues before production deployment.")
        
        return passed == total

if __name__ == "__main__":
    validator = ProductionServerValidator()
    success = validator.run_all_tests()
    sys.exit(0 if success else 1)
