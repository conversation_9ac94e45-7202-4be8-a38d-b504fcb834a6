# Comprehensive Manual Testing Checklist for Agno WorkSphere

## 🎯 Testing Overview
This checklist covers all functionality across user roles: **Viewer**, **Member**, **Admin**, and **Owner**.

## 🔐 Authentication & Registration Testing

### Registration Flow
- [ ] Navigate to `/register`
- [ ] Test form validation (empty fields, invalid email, weak password)
- [ ] Register new user with valid data
- [ ] Verify email validation
- [ ] Check redirect after successful registration

### Login Flow
- [ ] Navigate to `/login`
- [ ] Test form validation (empty fields, invalid credentials)
- [ ] Login with valid credentials
- [ ] Verify JWT token storage
- [ ] Check redirect to appropriate dashboard based on role

### Logout Flow
- [ ] Test logout functionality
- [ ] Verify token removal
- [ ] Check redirect to login page

## 🏢 Organization Management Testing

### Organization Creation (Owner Only)
- [ ] Navigate to organization settings
- [ ] Create new organization with all fields
- [ ] Test form validation
- [ ] Verify organization appears in dashboard
- [ ] Check database persistence

### Organization Settings
- [ ] Update organization details
- [ ] Change organization logo
- [ ] Modify contact information
- [ ] Test domain validation settings
- [ ] Verify changes are saved

### Member Management
- [ ] Invite new members via email
- [ ] Test domain-based invitations
- [ ] Change member roles (Owner only)
- [ ] Remove members (Owner/Admin only)
- [ ] Verify role-based access restrictions

## 📊 Dashboard Testing

### Role-Based Dashboard Access
- [ ] **Viewer**: Limited read-only access
- [ ] **Member**: Task management capabilities
- [ ] **Admin**: Project management within scope
- [ ] **Owner**: Full organization access

### Dashboard Components
- [ ] Project overview cards
- [ ] Quick actions based on role
- [ ] Recent activity feed
- [ ] Statistics and metrics
- [ ] Navigation menu items based on role

## 📋 Project Management Testing

### Project Creation
- [ ] **Owner/Admin**: Create new project
- [ ] **Member/Viewer**: Verify restricted access
- [ ] Test all project fields (name, description, dates, priority)
- [ ] Verify project appears in dashboard

### Project Overview
- [ ] Navigate to project overview page
- [ ] Test tab navigation (Overview, Tasks, Settings)
- [ ] Verify role-based tab visibility
- [ ] Check project details display
- [ ] Test "Go to Board" button

### AI Project Creation (Owner Only)
- [ ] Access AI project creation feature
- [ ] Test different project templates
- [ ] Verify AI-generated content
- [ ] Check task and workflow generation
- [ ] Confirm project creation with AI data

## 📋 Kanban Board Testing

### Board Access and Navigation
- [ ] Navigate to kanban board from project
- [ ] Verify board loads with correct project context
- [ ] Check default columns (To-Do, In Progress, Review, Done)
- [ ] Test responsive design on different screen sizes

### Column Management
- [ ] **Admin/Owner**: Add new columns
- [ ] **Admin/Owner**: Edit column names and colors
- [ ] **Admin/Owner**: Delete columns
- [ ] **Member/Viewer**: Verify restricted access
- [ ] Test column reordering

### Card Management
- [ ] **Admin/Owner**: Create new cards
- [ ] **Member**: Create cards in assigned projects
- [ ] **Viewer**: Verify read-only access
- [ ] Test card drag and drop between columns
- [ ] Verify card position updates

### Card Details
- [ ] Click on card to open details
- [ ] Test card title editing
- [ ] Update card description
- [ ] Change card priority
- [ ] Set due dates
- [ ] Assign/unassign members

## ✅ Checklist Functionality Testing

### Basic Checklist Operations
- [ ] Add checklist items to cards
- [ ] Mark items as complete/incomplete
- [ ] Edit checklist item text
- [ ] Delete checklist items
- [ ] Reorder checklist items

### AI Checklist Generation (Owner Only)
- [ ] Access AI checklist feature
- [ ] Generate checklist for different task types
- [ ] Verify AI-generated items are relevant
- [ ] Test bulk checklist operations
- [ ] Check confidence scores and metadata

### Role-Based Checklist Access
- [ ] **Owner**: Full AI and manual checklist access
- [ ] **Admin**: Manual checklist management
- [ ] **Member**: Limited checklist editing
- [ ] **Viewer**: Read-only checklist access

## 👥 Team Members Testing

### Member List Display
- [ ] Navigate to team members page
- [ ] Verify all organization members are listed
- [ ] Check member role display
- [ ] Test member search and filtering

### Member Management (Admin/Owner)
- [ ] Invite new members
- [ ] Change member roles
- [ ] Remove members
- [ ] Test bulk member operations

## 📈 Analytics Testing

### Analytics Dashboard
- [ ] Navigate to analytics page
- [ ] Verify project statistics
- [ ] Check task completion metrics
- [ ] Test date range filtering
- [ ] Verify role-based data access

### Report Generation
- [ ] Generate project reports
- [ ] Export analytics data
- [ ] Test different report formats
- [ ] Verify data accuracy

## 🔧 Settings Testing

### User Profile Settings
- [ ] Update personal information
- [ ] Change password
- [ ] Update notification preferences
- [ ] Test profile picture upload

### Organization Settings (Owner Only)
- [ ] Modify organization details
- [ ] Update billing information
- [ ] Change subscription plans
- [ ] Configure security settings

## 🔒 Role-Based Access Control Testing

### Navigation Restrictions
- [ ] **Viewer**: Limited navigation options
- [ ] **Member**: Task-focused navigation
- [ ] **Admin**: Project management navigation
- [ ] **Owner**: Full navigation access

### Feature Access Control
- [ ] AI features (Owner only)
- [ ] Organization management (Owner only)
- [ ] Project creation (Admin/Owner)
- [ ] Member management (Admin/Owner)
- [ ] Task assignment restrictions

## 📱 Responsive Design Testing

### Mobile Compatibility
- [ ] Test on mobile devices (320px-768px)
- [ ] Verify touch interactions
- [ ] Check mobile navigation menu
- [ ] Test card drag-and-drop on mobile

### Tablet Compatibility
- [ ] Test on tablet devices (768px-1024px)
- [ ] Verify layout adjustments
- [ ] Check touch and mouse interactions

### Desktop Compatibility
- [ ] Test on various desktop resolutions
- [ ] Verify keyboard shortcuts
- [ ] Check hover states and interactions

## 🔄 Real-Time Features Testing

### Live Updates
- [ ] Test real-time card updates
- [ ] Verify live collaboration features
- [ ] Check notification system
- [ ] Test websocket connections

## 🗄️ Database Persistence Testing

### Data Integrity
- [ ] Create data and refresh page
- [ ] Verify data persists after logout/login
- [ ] Test data relationships (projects, cards, checklists)
- [ ] Check data consistency across users

### CRUD Operations
- [ ] Create: All entity types
- [ ] Read: Data retrieval and display
- [ ] Update: Modifications and edits
- [ ] Delete: Removal and cleanup

## 🚨 Error Handling Testing

### Network Errors
- [ ] Test offline functionality
- [ ] Verify error messages for failed requests
- [ ] Check retry mechanisms

### Validation Errors
- [ ] Test form validation messages
- [ ] Verify input constraints
- [ ] Check error state handling

## ✅ Final Verification

### Cross-Browser Testing
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge

### Performance Testing
- [ ] Page load times
- [ ] Large dataset handling
- [ ] Memory usage
- [ ] Network request optimization

---

## 📝 Testing Notes

**Backend Status**: ✅ Running on http://localhost:3001
**Frontend Status**: ✅ Running on http://localhost:3000
**Database**: ✅ Connected and operational

**Test Results Summary**:
- API Root Endpoint: ✅ PASS
- Authentication endpoints: ✅ Available
- Protected routes: ✅ Require authentication
- Frontend: ✅ Accessible and responsive
