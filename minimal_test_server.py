#!/usr/bin/env python3
"""
Minimal test server to bypass startup issues and enable testing
"""
import sys
import os
import asyncio
import asyncpg
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

app = FastAPI(title="Agno WorkSphere Test API", version="1.0.0")

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database connection
DATABASE_URL = "postgresql://postgres:admin@localhost:5432/agno_worksphere"

class UserRegister(BaseModel):
    email: str
    password: str
    first_name: str
    last_name: str
    organization_name: str = None
    organization_domain: str = None

class UserLogin(BaseModel):
    email: str
    password: str

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        await conn.fetchval("SELECT 1")
        await conn.close()
        db_status = "connected"
    except Exception as e:
        db_status = f"error: {str(e)}"
    
    return {
        "status": "healthy",
        "timestamp": "2025-08-05T21:30:00Z",
        "database": db_status,
        "version": "1.0.0"
    }

@app.post("/api/auth/register")
async def register_user(user_data: UserRegister):
    """Register a new user"""
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Check if user exists
        existing = await conn.fetchrow(
            "SELECT id FROM users WHERE email = $1", user_data.email
        )
        if existing:
            raise HTTPException(status_code=400, detail="User already exists")
        
        # Generate UUID for user ID
        import uuid
        user_uuid = str(uuid.uuid4())

        # Create user with proper defaults and generated UUID
        user_id = await conn.fetchval("""
            INSERT INTO users (id, email, password_hash, first_name, last_name, email_verified, two_factor_enabled, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
            RETURNING id
        """, user_uuid, user_data.email, "hashed_" + user_data.password, user_data.first_name,
            user_data.last_name, False, False)
        
        await conn.close()
        
        return {
            "success": True,
            "message": "User registered successfully",
            "user_id": str(user_id),
            "email": user_data.email
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Registration failed: {str(e)}")

@app.post("/api/auth/login")
async def login_user(login_data: UserLogin):
    """Login user"""
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        user = await conn.fetchrow(
            "SELECT id, email, first_name, last_name FROM users WHERE email = $1", 
            login_data.email
        )
        
        await conn.close()
        
        if not user:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # Simple token (in real app, use JWT)
        token = f"token_{user['id']}"
        
        return {
            "success": True,
            "token": token,
            "user": {
                "id": str(user['id']),
                "email": user['email'],
                "first_name": user['first_name'],
                "last_name": user['last_name'],
                "role": "owner"  # Default for testing
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@app.get("/api/organizations")
async def get_organizations():
    """Get organizations"""
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        orgs = await conn.fetch(
            "SELECT id, name, description FROM organizations ORDER BY created_at DESC"
        )
        
        await conn.close()
        
        return {
            "success": True,
            "organizations": [dict(org) for org in orgs]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get organizations: {str(e)}")

@app.get("/api/projects")
async def get_projects():
    """Get projects"""
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        projects = await conn.fetch(
            "SELECT id, name, description, status FROM projects ORDER BY created_at DESC"
        )
        
        await conn.close()
        
        return {
            "success": True,
            "projects": [dict(project) for project in projects]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get projects: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting Minimal Test Server")
    print("📍 Server will be available at: http://localhost:3001")
    print("📚 API Documentation: http://localhost:3001/docs")
    
    uvicorn.run(
        "minimal_test_server:app",
        host="0.0.0.0",
        port=3001,
        reload=False,
        log_level="info"
    )
