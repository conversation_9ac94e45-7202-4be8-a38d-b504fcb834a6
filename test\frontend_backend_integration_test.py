#!/usr/bin/env python3
"""
Frontend-Backend Integration Analysis
Analyzes frontend code for proper backend integration
"""
import os
import re
import json
from typing import Dict, List, Any, Set

class FrontendBackendIntegrationAnalyzer:
    def __init__(self):
        self.frontend_path = "agnoworksphere/src"
        self.analysis_results = {
            "mock_data_usage": [],
            "hardcoded_data": [],
            "api_service_usage": [],
            "backend_endpoints_called": set(),
            "missing_error_handling": [],
            "proper_integrations": [],
            "issues_found": []
        }
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Analyze a single file for backend integration patterns"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_analysis = {
                "file": file_path,
                "has_mock_data": False,
                "has_hardcoded_data": False,
                "uses_api_service": False,
                "api_calls": [],
                "mock_patterns": [],
                "hardcoded_patterns": [],
                "error_handling": False
            }
            
            # Check for mock data patterns
            mock_patterns = [
                r'const\s+mock\w*\s*=',
                r'mockData',
                r'MOCK_',
                r'// Mock',
                r'\/\* Mock',
                r'fake\w*Data',
                r'dummyData',
                r'testData\s*='
            ]
            
            for pattern in mock_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    file_analysis["has_mock_data"] = True
                    file_analysis["mock_patterns"].extend(matches)
            
            # Check for hardcoded data arrays/objects
            hardcoded_patterns = [
                r'const\s+\w+\s*=\s*\[[\s\S]*?\{[\s\S]*?name\s*:\s*["\']',
                r'const\s+\w+\s*=\s*\{[\s\S]*?id\s*:\s*["\']',
                r'useState\(\[[\s\S]*?\{[\s\S]*?name\s*:'
            ]
            
            for pattern in hardcoded_patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                if matches:
                    file_analysis["has_hardcoded_data"] = True
                    file_analysis["hardcoded_patterns"].extend(matches[:3])  # Limit to first 3
            
            # Check for API service usage
            api_service_patterns = [
                r'import.*apiService',
                r'import.*realApiService',
                r'apiService\.',
                r'realApiService\.',
                r'authService\.',
                r'organizationService\.'
            ]
            
            for pattern in api_service_patterns:
                if re.search(pattern, content):
                    file_analysis["uses_api_service"] = True
                    break
            
            # Extract API calls
            api_call_patterns = [
                r'(apiService|realApiService|authService|organizationService)\.(\w+)\.(\w+)',
                r'fetch\(["\']([^"\']+)["\']',
                r'axios\.(get|post|put|delete)\(["\']([^"\']+)["\']'
            ]
            
            for pattern in api_call_patterns:
                matches = re.findall(pattern, content)
                file_analysis["api_calls"].extend(matches)
            
            # Check for error handling
            error_patterns = [
                r'try\s*\{',
                r'catch\s*\(',
                r'\.catch\(',
                r'error\s*=>'
            ]
            
            for pattern in error_patterns:
                if re.search(pattern, content):
                    file_analysis["error_handling"] = True
                    break
            
            return file_analysis
            
        except Exception as e:
            return {"file": file_path, "error": str(e)}
    
    def analyze_frontend_files(self):
        """Analyze all frontend files for backend integration"""
        print("🔍 Analyzing Frontend Files for Backend Integration...")
        
        # Key files to analyze
        key_files = [
            "utils/apiService.js",
            "utils/realApiService.js",
            "utils/authService.js",
            "utils/realAuthService.js",
            "utils/organizationService.js",
            "pages/login/components/LoginForm.jsx",
            "pages/register/components/RegisterForm.jsx",
            "pages/role-based-dashboard/index.jsx",
            "pages/project-management/index.jsx",
            "pages/organization-dashboard/index.jsx",
            "contexts/AuthContext.jsx",
            "contexts/ProjectContext.jsx"
        ]
        
        for file_name in key_files:
            file_path = os.path.join(self.frontend_path, file_name)
            if os.path.exists(file_path):
                analysis = self.analyze_file(file_path)
                self.process_file_analysis(analysis)
                print(f"  ✅ Analyzed: {file_name}")
            else:
                print(f"  ❌ Missing: {file_name}")
                self.analysis_results["issues_found"].append(f"Missing file: {file_name}")
    
    def process_file_analysis(self, analysis: Dict[str, Any]):
        """Process individual file analysis results"""
        if "error" in analysis:
            self.analysis_results["issues_found"].append(f"Error analyzing {analysis['file']}: {analysis['error']}")
            return
        
        file_name = os.path.basename(analysis["file"])
        
        # Track mock data usage
        if analysis["has_mock_data"]:
            self.analysis_results["mock_data_usage"].append({
                "file": file_name,
                "patterns": analysis["mock_patterns"]
            })
        
        # Track hardcoded data
        if analysis["has_hardcoded_data"]:
            self.analysis_results["hardcoded_data"].append({
                "file": file_name,
                "patterns": analysis["hardcoded_patterns"][:2]  # Limit output
            })
        
        # Track API service usage
        if analysis["uses_api_service"]:
            self.analysis_results["api_service_usage"].append(file_name)
        
        # Track API calls
        for call in analysis["api_calls"]:
            if isinstance(call, tuple) and len(call) >= 2:
                self.analysis_results["backend_endpoints_called"].add(str(call))
        
        # Track error handling
        if not analysis["error_handling"] and analysis["uses_api_service"]:
            self.analysis_results["missing_error_handling"].append(file_name)
        
        # Track proper integrations
        if analysis["uses_api_service"] and analysis["error_handling"] and not analysis["has_mock_data"]:
            self.analysis_results["proper_integrations"].append(file_name)
    
    def check_api_service_configuration(self):
        """Check API service configuration"""
        print("\n🔧 Checking API Service Configuration...")
        
        api_service_file = os.path.join(self.frontend_path, "utils/realApiService.js")
        if os.path.exists(api_service_file):
            with open(api_service_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check base URL configuration
            base_url_match = re.search(r'API_BASE_URL.*=.*["\']([^"\']+)["\']', content)
            if base_url_match:
                base_url = base_url_match.group(1)
                print(f"  ✅ API Base URL configured: {base_url}")
                
                # Check if it matches our backend
                if "localhost:3001" in base_url:
                    print(f"  ✅ Base URL matches backend server")
                else:
                    print(f"  ⚠️ Base URL may not match backend server")
            else:
                print(f"  ❌ API Base URL not found")
            
            # Check authentication headers
            if "Authorization" in content and "Bearer" in content:
                print(f"  ✅ Authentication headers configured")
            else:
                print(f"  ❌ Authentication headers missing")
            
            # Check error handling
            if "handleResponse" in content or "catch" in content:
                print(f"  ✅ Error handling present")
            else:
                print(f"  ❌ Error handling missing")
        else:
            print(f"  ❌ realApiService.js not found")
    
    def generate_integration_report(self):
        """Generate comprehensive integration report"""
        print("\n" + "=" * 60)
        print("📊 FRONTEND-BACKEND INTEGRATION REPORT")
        print("=" * 60)
        
        # Summary statistics
        total_files_analyzed = (len(self.analysis_results["api_service_usage"]) + 
                               len(self.analysis_results["mock_data_usage"]) + 
                               len(self.analysis_results["hardcoded_data"]))
        
        proper_integration_count = len(self.analysis_results["proper_integrations"])
        mock_usage_count = len(self.analysis_results["mock_data_usage"])
        hardcoded_count = len(self.analysis_results["hardcoded_data"])
        
        print(f"📈 Files with proper backend integration: {proper_integration_count}")
        print(f"⚠️ Files using mock data: {mock_usage_count}")
        print(f"🚨 Files with hardcoded data: {hardcoded_count}")
        print(f"🔧 Files using API services: {len(self.analysis_results['api_service_usage'])}")
        
        # Detailed findings
        if self.analysis_results["proper_integrations"]:
            print(f"\n✅ PROPERLY INTEGRATED FILES:")
            for file in self.analysis_results["proper_integrations"]:
                print(f"  - {file}")
        
        if self.analysis_results["mock_data_usage"]:
            print(f"\n⚠️ FILES USING MOCK DATA:")
            for item in self.analysis_results["mock_data_usage"]:
                print(f"  - {item['file']}: {len(item['patterns'])} mock patterns found")
        
        if self.analysis_results["hardcoded_data"]:
            print(f"\n🚨 FILES WITH HARDCODED DATA:")
            for item in self.analysis_results["hardcoded_data"]:
                print(f"  - {item['file']}: {len(item['patterns'])} hardcoded patterns found")
        
        if self.analysis_results["missing_error_handling"]:
            print(f"\n❌ FILES MISSING ERROR HANDLING:")
            for file in self.analysis_results["missing_error_handling"]:
                print(f"  - {file}")
        
        if self.analysis_results["issues_found"]:
            print(f"\n🚨 ISSUES FOUND:")
            for issue in self.analysis_results["issues_found"]:
                print(f"  - {issue}")
        
        # Calculate integration score
        if total_files_analyzed > 0:
            integration_score = (proper_integration_count / total_files_analyzed) * 100
            print(f"\n📊 Integration Score: {integration_score:.1f}%")
        else:
            print(f"\n📊 Integration Score: Unable to calculate")
        
        return self.analysis_results
    
    def run_analysis(self):
        """Run complete frontend-backend integration analysis"""
        print("🚀 Starting Frontend-Backend Integration Analysis...")
        print("=" * 60)
        
        self.analyze_frontend_files()
        self.check_api_service_configuration()
        return self.generate_integration_report()

if __name__ == "__main__":
    analyzer = FrontendBackendIntegrationAnalyzer()
    results = analyzer.run_analysis()
