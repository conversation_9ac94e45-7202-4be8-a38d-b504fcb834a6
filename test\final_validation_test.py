#!/usr/bin/env python3
"""
Final Validation Test for Agno WorkSphere
Comprehensive test of all fixes and improvements
"""
import requests
import json
import time
from typing import Dict, List, Any

BASE_URL = "http://localhost:3001"

class FinalValidationTest:
    def __init__(self):
        self.test_results = {
            "passed": 0,
            "failed": 0,
            "errors": [],
            "improvements": []
        }
        self.auth_token = None
    
    def log_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        if success:
            self.test_results["passed"] += 1
            print(f"✅ {test_name}: PASSED {details}")
        else:
            self.test_results["failed"] += 1
            self.test_results["errors"].append(f"{test_name}: {details}")
            print(f"❌ {test_name}: FAILED {details}")
    
    def log_improvement(self, improvement: str):
        """Log improvement made"""
        self.test_results["improvements"].append(improvement)
        print(f"🔧 IMPROVEMENT: {improvement}")
    
    def test_fixed_health_endpoints(self):
        """Test the fixed health and metrics endpoints"""
        print("\n🏥 Testing Fixed Health Endpoints...")
        
        # Test detailed health check
        try:
            response = requests.get(f"{BASE_URL}/health/detailed", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                required_fields = ["database", "version", "user_count"]
                missing_fields = [field for field in required_fields if field not in data]
                
                if not missing_fields:
                    self.log_result("Fixed Detailed Health Check", True, "All required fields present")
                    self.log_improvement("Added missing fields to detailed health endpoint")
                else:
                    self.log_result("Fixed Detailed Health Check", False, f"Still missing: {missing_fields}")
            else:
                self.log_result("Fixed Detailed Health Check", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Fixed Detailed Health Check", False, f"Error: {e}")
        
        # Test metrics endpoint
        try:
            response = requests.get(f"{BASE_URL}/metrics", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if "database" in data and "application" in data:
                    self.log_result("Fixed Metrics Endpoint", True, "Complete metrics data available")
                    self.log_improvement("Added application section to metrics endpoint")
                else:
                    self.log_result("Fixed Metrics Endpoint", False, "Still incomplete metrics data")
            else:
                self.log_result("Fixed Metrics Endpoint", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Fixed Metrics Endpoint", False, f"Error: {e}")
    
    def test_fixed_authentication(self):
        """Test the fixed authentication and authorization"""
        print("\n🔐 Testing Fixed Authentication...")
        
        # Test user registration and login
        try:
            # Register a test user
            registration_data = {
                "email": "<EMAIL>",
                "password": "finaltest123",
                "first_name": "Final",
                "last_name": "Test",
                "organization_name": "Final Test Org"
            }
            
            response = requests.post(
                f"{BASE_URL}/api/auth/register",
                json=registration_data,
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                self.log_result("User Registration", True, f"Status: {response.status_code}")
            else:
                self.log_result("User Registration", False, f"Status: {response.status_code}")
            
            # Login with the test user
            login_data = {
                "email": "<EMAIL>",
                "password": "finaltest123"
            }
            
            response = requests.post(
                f"{BASE_URL}/api/auth/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if "access_token" in data:
                    self.auth_token = data["access_token"]
                    self.log_result("User Login", True, "Token received")
                else:
                    self.log_result("User Login", False, "No token in response")
            else:
                self.log_result("User Login", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Authentication Flow", False, f"Error: {e}")
        
        # Test invalid token handling (should return 401, not 500)
        try:
            response = requests.get(
                f"{BASE_URL}/api/users/me",
                headers={"Authorization": "Bearer invalid_token_123"},
                timeout=10
            )
            
            if response.status_code == 401:
                self.log_result("Fixed Invalid Token Handling", True, "Returns 401 as expected")
                self.log_improvement("Fixed invalid token handling to return 401 instead of 500")
            else:
                self.log_result("Fixed Invalid Token Handling", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Fixed Invalid Token Handling", False, f"Error: {e}")
    
    def test_fixed_authorization(self):
        """Test the fixed authorization requirements"""
        print("\n🛡️ Testing Fixed Authorization...")
        
        # Test that protected endpoints now require authentication
        protected_endpoints = [
            ("/api/organizations", "Organizations"),
            ("/api/projects", "Projects"),
            ("/api/boards", "Boards")
        ]
        
        for endpoint, name in protected_endpoints:
            try:
                response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
                
                if response.status_code == 401:
                    self.log_result(f"Protected {name} Endpoint", True, "Requires authentication")
                    self.log_improvement(f"Added authentication requirement to {name} endpoint")
                else:
                    self.log_result(f"Protected {name} Endpoint", False, f"Status: {response.status_code}")
                    
            except Exception as e:
                self.log_result(f"Protected {name} Endpoint", False, f"Error: {e}")
        
        # Test authenticated access
        if self.auth_token:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            for endpoint, name in protected_endpoints:
                try:
                    response = requests.get(f"{BASE_URL}{endpoint}", headers=headers, timeout=10)
                    
                    if response.status_code == 200:
                        self.log_result(f"Authenticated {name} Access", True, "Access granted with valid token")
                    else:
                        self.log_result(f"Authenticated {name} Access", False, f"Status: {response.status_code}")
                        
                except Exception as e:
                    self.log_result(f"Authenticated {name} Access", False, f"Error: {e}")
    
    def test_response_times(self):
        """Test if response times have improved"""
        print("\n⚡ Testing Response Times...")
        
        endpoints_to_test = [
            ("/health", "Health Check"),
            ("/metrics", "Metrics")
        ]
        
        for endpoint, name in endpoints_to_test:
            try:
                start_time = time.time()
                response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
                end_time = time.time()
                
                response_time = end_time - start_time
                
                if response_time < 1.0:  # Less than 1 second
                    self.log_result(f"{name} Response Time", True, f"{response_time:.3f}s")
                elif response_time < 2.0:  # Less than 2 seconds (acceptable)
                    self.log_result(f"{name} Response Time", True, f"{response_time:.3f}s (acceptable)")
                else:
                    self.log_result(f"{name} Response Time", False, f"{response_time:.3f}s (still slow)")
                    
            except Exception as e:
                self.log_result(f"{name} Response Time", False, f"Error: {e}")
    
    def test_error_handling(self):
        """Test improved error handling"""
        print("\n🚨 Testing Error Handling...")
        
        # Test 404 for non-existent endpoints
        try:
            response = requests.get(f"{BASE_URL}/api/nonexistent", timeout=5)
            
            if response.status_code == 404:
                self.log_result("404 Error Handling", True, "Returns 404 for non-existent endpoints")
            else:
                self.log_result("404 Error Handling", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("404 Error Handling", False, f"Error: {e}")
        
        # Test 422 for malformed requests
        try:
            response = requests.post(
                f"{BASE_URL}/api/auth/login",
                json={"invalid": "data"},
                timeout=5
            )
            
            if response.status_code == 422:
                self.log_result("422 Error Handling", True, "Returns 422 for malformed requests")
            else:
                self.log_result("422 Error Handling", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("422 Error Handling", False, f"Error: {e}")
    
    def generate_final_report(self):
        """Generate final validation report"""
        print("\n" + "=" * 60)
        print("🎯 FINAL VALIDATION REPORT")
        print("=" * 60)
        
        print(f"✅ Tests Passed: {self.test_results['passed']}")
        print(f"❌ Tests Failed: {self.test_results['failed']}")
        
        if self.test_results["improvements"]:
            print(f"\n🔧 IMPROVEMENTS MADE ({len(self.test_results['improvements'])}):")
            for improvement in self.test_results["improvements"]:
                print(f"  - {improvement}")
        
        if self.test_results["errors"]:
            print(f"\n🚨 REMAINING ISSUES ({len(self.test_results['errors'])}):")
            for error in self.test_results["errors"]:
                print(f"  - {error}")
        
        # Calculate improvement score
        total_tests = self.test_results["passed"] + self.test_results["failed"]
        success_rate = (self.test_results["passed"] / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n📊 Overall Success Rate: {success_rate:.1f}%")
        print(f"🔧 Total Improvements: {len(self.test_results['improvements'])}")
        
        if success_rate >= 90:
            print("🟢 EXCELLENT - System is production-ready")
        elif success_rate >= 75:
            print("🟡 GOOD - Minor issues remain")
        elif success_rate >= 60:
            print("🟠 FAIR - Some issues need attention")
        else:
            print("🔴 NEEDS WORK - Significant issues remain")
        
        return self.test_results
    
    def run_final_validation(self):
        """Run complete final validation"""
        print("🚀 Starting Final Validation Test...")
        print("=" * 60)
        
        start_time = time.time()
        
        self.test_fixed_health_endpoints()
        self.test_fixed_authentication()
        self.test_fixed_authorization()
        self.test_response_times()
        self.test_error_handling()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n⏱️ Total Validation Time: {duration:.2f} seconds")
        
        return self.generate_final_report()

if __name__ == "__main__":
    tester = FinalValidationTest()
    results = tester.run_final_validation()
