# Browser Testing Guide for Agno WorkSphere

## 🌐 Access Information
- **Frontend URL**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/docs

## 🔐 Test User Accounts

### Quick Test Credentials
The system includes demo credentials for testing different roles:

1. **Owner Account**
   - Email: `<EMAIL>`
   - Password: `demo123`
   - Access: Full system access, AI features, organization management

2. **Admin Account**
   - Email: `<EMAIL>`
   - Password: `demo123`
   - Access: Project management, team management

3. **Member Account**
   - Email: `<EMAIL>`
   - Password: `demo123`
   - Access: Task management, assigned projects

4. **Viewer Account**
   - Email: `<EMAIL>`
   - Password: `demo123`
   - Access: Read-only access

## 🧪 Manual Testing Checklist

### 1. Authentication Flow Testing
- [ ] Navigate to http://localhost:3000
- [ ] Verify redirect to login page
- [ ] Test login with each role account
- [ ] Verify role-appropriate dashboard loads
- [ ] Test logout functionality

### 2. Dashboard Testing by Role

#### Owner Dashboard
- [ ] Access to all navigation items
- [ ] Organization management options
- [ ] AI project creation features
- [ ] Full project visibility
- [ ] Analytics and reporting access

#### Admin Dashboard
- [ ] Project management capabilities
- [ ] Team member management
- [ ] Limited organization settings
- [ ] Project creation abilities

#### Member Dashboard
- [ ] Task-focused interface
- [ ] Assigned project access
- [ ] Limited navigation options
- [ ] Task creation in assigned projects

#### Viewer Dashboard
- [ ] Read-only interface
- [ ] Limited navigation
- [ ] View-only project access
- [ ] No creation capabilities

### 3. Kanban Board Testing
- [ ] Navigate to kanban board from dashboard
- [ ] Verify default columns appear (To-Do, In Progress, Review, Done)
- [ ] Test card creation (role-dependent)
- [ ] Test drag and drop functionality
- [ ] Click on card to open details
- [ ] Test column management (admin/owner only)

### 4. Card Details Testing
- [ ] Open card details from kanban board
- [ ] Edit card title and description
- [ ] Assign/unassign team members
- [ ] Set due dates
- [ ] Add/edit labels
- [ ] Manage checklist items
- [ ] Test AI checklist generation (owner only)
- [ ] Add comments to activity timeline

### 5. Organization Management Testing (Owner Only)
- [ ] Navigate to organization settings
- [ ] Update organization information
- [ ] Manage team members
- [ ] Test member role changes
- [ ] Invite new members
- [ ] Configure organization settings

### 6. Project Management Testing
- [ ] Create new project (admin/owner)
- [ ] Edit project details
- [ ] Navigate to project overview
- [ ] Test project context switching
- [ ] Verify project-specific data loading

### 7. AI Features Testing (Owner Only)
- [ ] Access AI project creation
- [ ] Generate project with AI
- [ ] Test AI checklist generation in cards
- [ ] Verify AI-generated content quality
- [ ] Check AI confidence scores

### 8. Responsive Design Testing
- [ ] Test on desktop (1920x1080)
- [ ] Test on tablet (768x1024)
- [ ] Test on mobile (375x667)
- [ ] Verify touch interactions on mobile
- [ ] Check navigation menu on different sizes

### 9. Real-time Features Testing
- [ ] Open same project in multiple browser tabs
- [ ] Create/edit cards in one tab
- [ ] Verify updates appear in other tabs
- [ ] Test collaborative editing
- [ ] Check notification system

### 10. Error Handling Testing
- [ ] Test with invalid login credentials
- [ ] Try accessing restricted features
- [ ] Test form validation errors
- [ ] Verify error messages are user-friendly
- [ ] Test network error scenarios

## 🔍 Specific Feature Verification

### Navigation Testing
1. **Header Navigation**
   - [ ] Logo click redirects to appropriate dashboard
   - [ ] User menu functions correctly
   - [ ] Organization switcher (owner only)
   - [ ] Notification center

2. **Sidebar Navigation**
   - [ ] Role-appropriate menu items
   - [ ] Active page highlighting
   - [ ] Collapsible sidebar functionality

### Form Testing
1. **Registration Form**
   - [ ] Email validation
   - [ ] Password strength requirements
   - [ ] Organization creation flow
   - [ ] Success/error messaging

2. **Project Creation Form**
   - [ ] Required field validation
   - [ ] Date picker functionality
   - [ ] Priority selection
   - [ ] AI project generation (owner)

3. **Card Creation Form**
   - [ ] Title and description fields
   - [ ] Member assignment
   - [ ] Due date selection
   - [ ] Priority setting
   - [ ] Label management

### Data Persistence Verification
1. **Create Test Data**
   - [ ] Create organization
   - [ ] Create project
   - [ ] Create board and columns
   - [ ] Create cards with checklists
   - [ ] Add comments and attachments

2. **Verify Persistence**
   - [ ] Refresh browser
   - [ ] Logout and login
   - [ ] Switch between projects
   - [ ] Verify all data remains

## 🚨 Critical Issues to Watch For

### Security Issues
- [ ] Unauthorized access to restricted features
- [ ] Data leakage between organizations
- [ ] Role escalation vulnerabilities
- [ ] Insecure API endpoints

### Performance Issues
- [ ] Slow page load times
- [ ] Unresponsive UI interactions
- [ ] Memory leaks in long sessions
- [ ] Large dataset handling

### UI/UX Issues
- [ ] Broken layouts on different screen sizes
- [ ] Inconsistent styling
- [ ] Poor accessibility (keyboard navigation, screen readers)
- [ ] Confusing user flows

### Data Issues
- [ ] Data not saving properly
- [ ] Inconsistent data display
- [ ] Lost data on page refresh
- [ ] Incorrect role-based data filtering

## ✅ Expected Results

### Successful Test Indicators
- [ ] All pages load without errors
- [ ] Role-based access control works correctly
- [ ] Data persists across sessions
- [ ] Real-time updates function properly
- [ ] AI features work for owners
- [ ] Responsive design adapts correctly
- [ ] Forms validate and submit successfully
- [ ] Navigation works intuitively

### Performance Benchmarks
- [ ] Page load time < 3 seconds
- [ ] API response time < 1 second
- [ ] Smooth animations and transitions
- [ ] No console errors or warnings

## 📝 Testing Notes Template

```
Test Date: ___________
Tester: ___________
Browser: ___________
Screen Resolution: ___________

Role Tested: ___________
Features Tested:
- [ ] Authentication
- [ ] Dashboard
- [ ] Kanban Board
- [ ] Card Details
- [ ] Organization Management
- [ ] Project Management
- [ ] AI Features

Issues Found:
1. ___________
2. ___________
3. ___________

Overall Assessment:
- Functionality: ___/10
- Performance: ___/10
- User Experience: ___/10
- Security: ___/10

Additional Comments:
___________
```

## 🎯 Quick Verification Commands

### API Health Check
```bash
curl http://localhost:3001/api/v1/
```

### Frontend Health Check
```bash
curl http://localhost:3000
```

### Database Connection Test
```bash
# Check if backend can connect to database
curl http://localhost:3001/api/v1/health
```

---

**Note**: This guide should be used in conjunction with the automated test results to ensure comprehensive system verification.
