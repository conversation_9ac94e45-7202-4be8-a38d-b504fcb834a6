#!/usr/bin/env python3
"""
Production-Ready Enhanced FastAPI Server
Fully production-ready version with database integration, caching, and monitoring
"""
import asyncio
import logging
import time
import uuid
import json
import os
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional, List
from datetime import datetime

import asyncpg
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, Request, Response, BackgroundTasks, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel, EmailStr, ValidationError

# Import production components with fallbacks
try:
    from app.config import settings
except ImportError:
    # Fallback configuration
    class Settings:
        app_name = "Agno WorkSphere API"
        app_version = "2.0.0"
        environment = "production"
        allowed_origins = ["http://localhost:3000", "http://localhost:3001"]
        database_url = "postgresql://postgres:admin@localhost:5432/test_agnoworksphere"
        redis_url = "redis://localhost:6379"
        db_pool_min_size = 10
        db_pool_max_size = 20
        cache_ttl = 300
        smtp_user = ""
        smtp_pass = ""
    settings = Settings()

try:
    from app.core.security import hash_password, verify_password, create_access_token, verify_token
except ImportError:
    # Fallback security functions
    import hashlib
    import jwt
    from datetime import timedelta
    
    def hash_password(password: str) -> str:
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(password: str, hashed: str) -> bool:
        return hash_password(password) == hashed
    
    def create_access_token(data: dict) -> str:
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=15)
        to_encode.update({"exp": expire})
        return jwt.encode(to_encode, "secret-key", algorithm="HS256")
    
    def verify_token(token: str) -> dict:
        return jwt.decode(token, "secret-key", algorithms=["HS256"])

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = "postgresql://postgres:admin@localhost:5432/test_agnoworksphere"
DB_POOL_MIN_SIZE = 10
DB_POOL_MAX_SIZE = 20

# Global database pool
db_pool: Optional[asyncpg.Pool] = None

class DatabaseManager:
    """Production-ready database connection pool manager"""
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.stats = {"queries": 0, "errors": 0, "total_time": 0.0}
    
    async def initialize(self):
        """Initialize database connection pool"""
        try:
            self.pool = await asyncpg.create_pool(
                DATABASE_URL,
                min_size=DB_POOL_MIN_SIZE,
                max_size=DB_POOL_MAX_SIZE,
                max_queries=50000,
                max_inactive_connection_lifetime=300,
                command_timeout=60
            )
            logger.info(f"Database pool initialized: {DB_POOL_MIN_SIZE}-{DB_POOL_MAX_SIZE} connections")
            
            # Test connection
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
                logger.info("Database connection test successful")
                
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            raise
    
    async def close(self):
        """Close database connection pool"""
        if self.pool:
            await self.pool.close()
            logger.info("Database pool closed")
    
    async def get_connection(self):
        """Get database connection from pool"""
        if not self.pool:
            raise HTTPException(status_code=500, detail="Database pool not initialized")
        return self.pool.acquire()
    
    async def execute_query(self, query: str, *args, fetch_type: str = "fetchval"):
        """Execute query with monitoring"""
        start_time = time.time()
        try:
            async with self.get_connection() as conn:
                if fetch_type == "fetchval":
                    result = await conn.fetchval(query, *args)
                elif fetch_type == "fetchrow":
                    result = await conn.fetchrow(query, *args)
                elif fetch_type == "fetch":
                    result = await conn.fetch(query, *args)
                else:
                    result = await conn.execute(query, *args)
                
                query_time = time.time() - start_time
                self.stats["queries"] += 1
                self.stats["total_time"] += query_time
                
                if query_time > 1.0:  # Log slow queries
                    logger.warning(f"Slow query: {query_time:.3f}s - {query[:100]}...")
                
                return result
        except Exception as e:
            self.stats["errors"] += 1
            logger.error(f"Query failed: {e}")
            raise

class CacheManager:
    """Redis cache manager with fallback"""
    
    def __init__(self):
        self.redis = None
        self.enabled = False
        self.stats = {"hits": 0, "misses": 0, "errors": 0}
    
    async def initialize(self):
        """Initialize Redis connection with fallback"""
        try:
            import redis.asyncio as redis
            self.redis = redis.from_url("redis://localhost:6379", decode_responses=True)
            await self.redis.ping()
            self.enabled = True
            logger.info("Redis cache initialized successfully")
        except Exception as e:
            logger.warning(f"Redis not available, caching disabled: {e}")
            self.enabled = False
    
    async def get(self, key: str, default: Any = None) -> Any:
        """Get value from cache with fallback"""
        if not self.enabled:
            self.stats["misses"] += 1
            return default
        
        try:
            value = await self.redis.get(key)
            if value is not None:
                self.stats["hits"] += 1
                try:
                    return json.loads(value)
                except:
                    return value
            else:
                self.stats["misses"] += 1
                return default
        except Exception as e:
            self.stats["errors"] += 1
            logger.warning(f"Cache get failed: {e}")
            return default
    
    async def set(self, key: str, value: Any, ttl: int = 300) -> bool:
        """Set value in cache with fallback"""
        if not self.enabled:
            return False
        
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value, default=str)
            await self.redis.setex(key, ttl, value)
            return True
        except Exception as e:
            self.stats["errors"] += 1
            logger.warning(f"Cache set failed: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache"""
        if not self.enabled:
            return False
        
        try:
            await self.redis.delete(key)
            return True
        except Exception as e:
            self.stats["errors"] += 1
            return False

# Global managers
db_manager = DatabaseManager()
cache_manager = CacheManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting Production-Ready Enhanced Server")
    
    # Initialize database
    await db_manager.initialize()
    
    # Initialize cache
    await cache_manager.initialize()
    
    # Initialize demo data if needed
    await initialize_demo_data()
    
    logger.info("Server startup completed")
    
    yield
    
    # Shutdown
    logger.info("Shutting down server")
    await db_manager.close()
    logger.info("Server shutdown completed")

# Create FastAPI app
app = FastAPI(
    title="Agno WorkSphere Production API",
    description="Production-ready enhanced project management API",
    version="2.0.0",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request monitoring middleware
@app.middleware("http")
async def monitor_requests(request: Request, call_next):
    """Monitor requests with timing and logging"""
    start_time = time.time()
    request_id = str(uuid.uuid4())
    
    logger.info(f"Request started: {request.method} {request.url.path} [{request_id}]")
    
    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        
        logger.info(f"Request completed: {request.method} {request.url.path} "
                   f"[{response.status_code}] [{process_time:.3f}s] [{request_id}]")
        
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = f"{process_time:.3f}"
        
        return response
        
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(f"Request failed: {request.method} {request.url.path} "
                    f"[{process_time:.3f}s] [{request_id}] - {e}")
        raise

# Exception handlers
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    logger.error(f"Validation error on {request.method} {request.url}: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={
            "detail": exc.errors(),
            "message": "Validation failed"
        }
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "message": "An unexpected error occurred"
        }
    )

async def initialize_demo_data():
    """Initialize demo data in database"""
    try:
        async with db_manager.get_connection() as conn:
            # Check if demo data already exists
            user_count = await conn.fetchval("SELECT COUNT(*) FROM users")
            if user_count > 0:
                logger.info("Demo data already exists, skipping initialization")
                return
            
            logger.info("Initializing demo data...")
            
            # Create demo organization
            org_id = str(uuid.uuid4())
            await conn.execute("""
                INSERT INTO organizations (id, name, description, domain, created_by)
                VALUES ($1, $2, $3, $4, $5)
            """, org_id, "ACME Corporation", "Demo organization for testing", "acme.com", None)
            
            # Create demo users
            demo_users = [
                {"email": "<EMAIL>", "password": "Owner123!", "first_name": "John", "last_name": "Owner", "role": "owner"},
                {"email": "<EMAIL>", "password": "Admin123!", "first_name": "Jane", "last_name": "Admin", "role": "admin"},
                {"email": "<EMAIL>", "password": "Member123!", "first_name": "Bob", "last_name": "Member", "role": "member"},
                {"email": "<EMAIL>", "password": "Viewer123!", "first_name": "Alice", "last_name": "Viewer", "role": "viewer"}
            ]
            
            for user_data in demo_users:
                user_id = str(uuid.uuid4())
                hashed_password = hash_password(user_data["password"])
                
                # Insert user
                await conn.execute("""
                    INSERT INTO users (id, email, password_hash, first_name, last_name, email_verified)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, user_id, user_data["email"], hashed_password, user_data["first_name"], 
                    user_data["last_name"], True)
                
                # Add to organization
                await conn.execute("""
                    INSERT INTO organization_members (user_id, organization_id, role)
                    VALUES ($1, $2, $3)
                """, user_id, org_id, user_data["role"])
                
                # Update organization created_by for owner
                if user_data["role"] == "owner":
                    await conn.execute("""
                        UPDATE organizations SET created_by = $1 WHERE id = $2
                    """, user_id, org_id)
            
            logger.info("Demo data initialized successfully")
            
    except Exception as e:
        logger.error(f"Failed to initialize demo data: {e}")

# Dependency to get database connection
async def get_db():
    """Dependency to get database connection"""
    async with db_manager.get_connection() as conn:
        yield conn

# Dependency to get current user
def get_user_from_token(authorization: Optional[str] = Header(None)):
    """Get user from authorization token"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")
    
    try:
        token = authorization.replace("Bearer ", "")
        payload = verify_token(token)
        return payload
    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid token")

# Pydantic models
class UserRegister(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: Optional[str] = ""
    organization_name: Optional[str] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int = 900

class UserResponse(BaseModel):
    id: str
    email: str
    first_name: str
    last_name: str
    email_verified: bool = True
    created_at: str
    role: Optional[str] = None

class OrganizationResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    domain: Optional[str] = None
    created_at: str
    member_count: int = 1

class ProjectResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    organization_id: str
    status: str
    priority: str
    progress: int
    created_at: str

class AIGeneratedProjectCreate(BaseModel):
    name: str
    description: str
    generated_tasks: dict
    organization_id: str

class AIGeneratedProjectResponse(BaseModel):
    id: str
    name: str
    description: str
    generated_tasks: dict
    organization_id: str
    created_by: str
    created_at: str

# API Routes
@app.get("/health")
async def health_check(db: asyncpg.Connection = Depends(get_db)):
    """Enhanced health check with database and cache status"""
    try:
        # Test database
        await db.fetchval("SELECT 1")
        db_status = "connected"

        # Get pool stats
        pool_stats = {
            "size": db_manager.pool.get_size() if db_manager.pool else 0,
            "idle": db_manager.pool.get_idle_size() if db_manager.pool else 0,
            "queries_executed": db_manager.stats["queries"],
            "total_errors": db_manager.stats["errors"],
            "avg_query_time": (
                db_manager.stats["total_time"] / db_manager.stats["queries"]
                if db_manager.stats["queries"] > 0 else 0
            )
        }

        # Cache status
        cache_status = "enabled" if cache_manager.enabled else "disabled"

    except Exception as e:
        db_status = f"error: {str(e)}"
        pool_stats = {}
        cache_status = "error"

    return {
        "status": "healthy" if db_status == "connected" else "degraded",
        "timestamp": datetime.utcnow().isoformat(),
        "database": db_status,
        "cache": cache_status,
        "pool_stats": pool_stats,
        "cache_stats": cache_manager.stats,
        "version": "2.0.0"
    }

@app.get("/health/detailed")
async def detailed_health_check(db: asyncpg.Connection = Depends(get_db)):
    """Comprehensive health check"""
    health_data = {
        "overall_status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "components": {}
    }

    # Database health
    try:
        await db.fetchval("SELECT 1")
        user_count = await db.fetchval("SELECT COUNT(*) FROM users")
        org_count = await db.fetchval("SELECT COUNT(*) FROM organizations")

        health_data["components"]["database"] = {
            "status": "healthy",
            "user_count": user_count,
            "organization_count": org_count,
            "pool_size": db_manager.pool.get_size() if db_manager.pool else 0
        }
    except Exception as e:
        health_data["components"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_data["overall_status"] = "unhealthy"

    # Cache health
    health_data["components"]["cache"] = {
        "status": "healthy" if cache_manager.enabled else "disabled",
        "stats": cache_manager.stats
    }

    return health_data

@app.post("/api/auth/register", response_model=dict)
async def register_user(user_data: UserRegister, db: asyncpg.Connection = Depends(get_db)):
    """Register a new user with database persistence"""
    try:
        # Check cache first
        cache_key = f"user_exists:{user_data.email}"
        cached_result = await cache_manager.get(cache_key)

        if cached_result == "exists":
            raise HTTPException(status_code=400, detail="User already exists")

        # Check if user exists in database
        existing = await db.fetchrow("SELECT id FROM users WHERE email = $1", user_data.email)
        if existing:
            await cache_manager.set(cache_key, "exists", ttl=3600)
            raise HTTPException(status_code=400, detail="User already exists")

        # Generate UUID and hash password
        user_id = str(uuid.uuid4())
        hashed_password = hash_password(user_data.password)

        # Create user
        await db.execute("""
            INSERT INTO users (id, email, password_hash, first_name, last_name, email_verified)
            VALUES ($1, $2, $3, $4, $5, $6)
        """, user_id, user_data.email, hashed_password, user_data.first_name,
            user_data.last_name or "", True)

        # Create organization if provided
        if user_data.organization_name:
            org_id = str(uuid.uuid4())
            await db.execute("""
                INSERT INTO organizations (id, name, description, created_by)
                VALUES ($1, $2, $3, $4)
            """, org_id, user_data.organization_name,
                f"Organization created by {user_data.first_name}", user_id)

            # Add user as owner
            await db.execute("""
                INSERT INTO organization_members (user_id, organization_id, role)
                VALUES ($1, $2, $3)
            """, user_id, org_id, "owner")

        # Invalidate cache
        await cache_manager.delete(cache_key)

        logger.info(f"User registered: {user_data.email} [{user_id}]")

        return {
            "success": True,
            "message": "User registered successfully",
            "user_id": user_id,
            "email": user_data.email
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration failed for {user_data.email}: {e}")
        raise HTTPException(status_code=500, detail=f"Registration failed: {str(e)}")

@app.post("/api/auth/login", response_model=dict)
async def login_user(login_data: UserLogin, db: asyncpg.Connection = Depends(get_db)):
    """Login user with caching"""
    try:
        # Check cache first
        cache_key = f"user_login:{login_data.email}"
        cached_user = await cache_manager.get(cache_key)

        if not cached_user:
            # Get from database
            user = await db.fetchrow("""
                SELECT u.id, u.email, u.first_name, u.last_name, u.password_hash,
                       om.role
                FROM users u
                LEFT JOIN organization_members om ON u.id = om.user_id
                WHERE u.email = $1
                LIMIT 1
            """, login_data.email)

            if not user:
                raise HTTPException(status_code=401, detail="Invalid credentials")

            # Verify password
            if not verify_password(login_data.password, user['password_hash']):
                raise HTTPException(status_code=401, detail="Invalid credentials")

            # Cache user data (without password)
            user_data = {
                "id": str(user['id']),
                "email": user['email'],
                "first_name": user['first_name'],
                "last_name": user['last_name'],
                "role": user['role'] or "member"
            }
            await cache_manager.set(cache_key, user_data, ttl=1800)
        else:
            user_data = cached_user

        # Generate token
        token_data = {"sub": user_data["id"], "email": user_data["email"]}
        token = create_access_token(token_data)

        logger.info(f"User logged in: {user_data['email']} [{user_data['id']}]")

        return {
            "success": True,
            "access_token": token,
            "token_type": "bearer",
            "expires_in": 900,
            "user": user_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login failed for {login_data.email}: {e}")
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@app.get("/api/organizations", response_model=List[OrganizationResponse])
async def get_organizations(db: asyncpg.Connection = Depends(get_db)):
    """Get organizations with caching"""
    try:
        cache_key = "organizations:all"
        cached_orgs = await cache_manager.get(cache_key)

        if cached_orgs:
            organizations = cached_orgs
        else:
            # Get from database with member count
            orgs = await db.fetch("""
                SELECT o.id, o.name, o.description, o.domain, o.created_at,
                       COUNT(om.user_id) as member_count
                FROM organizations o
                LEFT JOIN organization_members om ON o.id = om.organization_id
                GROUP BY o.id, o.name, o.description, o.domain, o.created_at
                ORDER BY o.created_at DESC
            """)

            organizations = []
            for org in orgs:
                organizations.append({
                    "id": str(org['id']),
                    "name": org['name'],
                    "description": org['description'],
                    "domain": org['domain'],
                    "created_at": org['created_at'].isoformat(),
                    "member_count": org['member_count']
                })

            # Cache results
            await cache_manager.set(cache_key, organizations, ttl=600)

        logger.info(f"Organizations retrieved: {len(organizations)}")
        return organizations

    except Exception as e:
        logger.error(f"Failed to get organizations: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get organizations: {str(e)}")

@app.get("/api/projects", response_model=List[ProjectResponse])
async def get_projects(db: asyncpg.Connection = Depends(get_db)):
    """Get projects with caching"""
    try:
        cache_key = "projects:all"
        cached_projects = await cache_manager.get(cache_key)

        if cached_projects:
            projects = cached_projects
        else:
            # Get from database
            project_rows = await db.fetch("""
                SELECT id, name, description, organization_id, status, priority,
                       progress, created_at
                FROM projects
                ORDER BY created_at DESC
            """)

            projects = []
            for project in project_rows:
                projects.append({
                    "id": str(project['id']),
                    "name": project['name'],
                    "description": project['description'],
                    "organization_id": str(project['organization_id']),
                    "status": project['status'],
                    "priority": project['priority'],
                    "progress": project['progress'],
                    "created_at": project['created_at'].isoformat()
                })

            # Cache results
            await cache_manager.set(cache_key, projects, ttl=300)

        logger.info(f"Projects retrieved: {len(projects)}")
        return projects

    except Exception as e:
        logger.error(f"Failed to get projects: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get projects: {str(e)}")

@app.post("/api/ai-projects", response_model=AIGeneratedProjectResponse)
async def create_ai_project(
    project_data: AIGeneratedProjectCreate,
    db: asyncpg.Connection = Depends(get_db),
    current_user: dict = Depends(get_user_from_token)
):
    """Create AI-generated project"""
    try:
        project_id = str(uuid.uuid4())

        # Insert AI project
        await db.execute("""
            INSERT INTO ai_generated_projects (id, name, description, generated_tasks,
                                             organization_id, created_by)
            VALUES ($1, $2, $3, $4, $5, $6)
        """, project_id, project_data.name, project_data.description,
            json.dumps(project_data.generated_tasks), project_data.organization_id,
            current_user["sub"])

        # Get the created project
        project = await db.fetchrow("""
            SELECT id, name, description, generated_tasks, organization_id,
                   created_by, created_at
            FROM ai_generated_projects
            WHERE id = $1
        """, project_id)

        # Invalidate cache
        await cache_manager.delete("ai_projects:all")

        result = {
            "id": str(project['id']),
            "name": project['name'],
            "description": project['description'],
            "generated_tasks": json.loads(project['generated_tasks']),
            "organization_id": str(project['organization_id']),
            "created_by": str(project['created_by']),
            "created_at": project['created_at'].isoformat()
        }

        logger.info(f"AI project created: {project_data.name} [{project_id}]")
        return result

    except Exception as e:
        logger.error(f"Failed to create AI project: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create AI project: {str(e)}")

@app.get("/api/ai-projects", response_model=List[AIGeneratedProjectResponse])
async def get_ai_projects(db: asyncpg.Connection = Depends(get_db)):
    """Get AI-generated projects"""
    try:
        cache_key = "ai_projects:all"
        cached_projects = await cache_manager.get(cache_key)

        if cached_projects:
            projects = cached_projects
        else:
            # Get from database
            project_rows = await db.fetch("""
                SELECT id, name, description, generated_tasks, organization_id,
                       created_by, created_at
                FROM ai_generated_projects
                ORDER BY created_at DESC
            """)

            projects = []
            for project in project_rows:
                projects.append({
                    "id": str(project['id']),
                    "name": project['name'],
                    "description": project['description'],
                    "generated_tasks": json.loads(project['generated_tasks']),
                    "organization_id": str(project['organization_id']),
                    "created_by": str(project['created_by']),
                    "created_at": project['created_at'].isoformat()
                })

            # Cache results
            await cache_manager.set(cache_key, projects, ttl=300)

        logger.info(f"AI projects retrieved: {len(projects)}")
        return projects

    except Exception as e:
        logger.error(f"Failed to get AI projects: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get AI projects: {str(e)}")

@app.get("/api/users/me", response_model=UserResponse)
async def get_current_user(
    db: asyncpg.Connection = Depends(get_db),
    current_user: dict = Depends(get_user_from_token)
):
    """Get current user profile"""
    try:
        user = await db.fetchrow("""
            SELECT u.id, u.email, u.first_name, u.last_name, u.email_verified,
                   u.created_at, om.role
            FROM users u
            LEFT JOIN organization_members om ON u.id = om.user_id
            WHERE u.id = $1
            LIMIT 1
        """, current_user["sub"])

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "id": str(user['id']),
            "email": user['email'],
            "first_name": user['first_name'],
            "last_name": user['last_name'],
            "email_verified": user['email_verified'],
            "created_at": user['created_at'].isoformat(),
            "role": user['role'] or "member"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get current user: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get user: {str(e)}")

@app.get("/metrics")
async def get_metrics(db: asyncpg.Connection = Depends(get_db)):
    """Get application metrics"""
    try:
        # Database metrics
        db_stats = await db.fetchrow("""
            SELECT
                (SELECT COUNT(*) FROM users) as user_count,
                (SELECT COUNT(*) FROM organizations) as org_count,
                (SELECT COUNT(*) FROM projects) as project_count,
                (SELECT COUNT(*) FROM ai_generated_projects) as ai_project_count,
                (SELECT COUNT(*) FROM cards) as task_count
        """)

        return {
            "database": dict(db_stats),
            "pool_stats": {
                "size": db_manager.pool.get_size() if db_manager.pool else 0,
                "idle": db_manager.pool.get_idle_size() if db_manager.pool else 0,
                "queries_executed": db_manager.stats["queries"],
                "errors": db_manager.stats["errors"],
                "avg_query_time": (
                    db_manager.stats["total_time"] / db_manager.stats["queries"]
                    if db_manager.stats["queries"] > 0 else 0
                )
            },
            "cache_stats": cache_manager.stats,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")

@app.get("/api/organizations/{org_id}/projects")
async def get_organization_projects(
    org_id: str,
    db: asyncpg.Connection = Depends(get_db)
):
    """Get projects for a specific organization"""
    try:
        projects = await db.fetch("""
            SELECT id, name, description, status, priority, progress, created_at
            FROM projects
            WHERE organization_id = $1
            ORDER BY created_at DESC
        """, org_id)

        result = []
        for project in projects:
            result.append({
                "id": str(project['id']),
                "name": project['name'],
                "description": project['description'],
                "status": project['status'],
                "priority": project['priority'],
                "progress": project['progress'],
                "created_at": project['created_at'].isoformat()
            })

        return {"success": True, "projects": result}

    except Exception as e:
        logger.error(f"Failed to get organization projects: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get projects: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "production_ready_enhanced_server:app",
        host="0.0.0.0",
        port=3001,
        reload=False,
        log_level="info",
        access_log=True
    )
