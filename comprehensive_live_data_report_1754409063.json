{"timestamp": "2025-08-05T21:20:46.190323", "phases": {"Phase 1": {"tests": [{"name": "PostgreSQL Connection", "status": "PASS", "details": "Connected successfully: PostgreSQL 16.8, compiled by Visual C++ build 1942..."}, {"name": "Database Schema", "status": "PASS", "details": "All 8 required tables exist"}, {"name": "Foreign Key Constraints", "status": "PASS", "details": "Found 28 foreign key constraints"}, {"name": "Database Connection", "status": "FAIL", "details": "null value in column \"email_verified\" of relation \"users\" violates not-null constraint\nDETAIL:  Failing row contains (e2c8d038-f7d7-4e43-b6fd-1a5cde555357, <EMAIL>, hashed_password, Test, User, null, null, null, null, null, 2025-08-05 21:20:46.625494+05:30, 2025-08-05 21:20:46.625494+05:30)."}], "score": 75.0, "max_score": 100, "critical_issues": ["Database connection failed: null value in column \"email_verified\" of relation \"users\" violates not-null constraint\nDETAIL:  Failing row contains (e2c8d038-f7d7-4e43-b6fd-1a5cde555357, <EMAIL>, hashed_password, Test, User, null, null, null, null, null, 2025-08-05 21:20:46.625494+05:30, 2025-08-05 21:20:46.625494+05:30)."], "live_data_verified": ["PostgreSQL 16.8, connected", "All 8 database tables verified", "28 foreign key constraints verified"], "database_stats": {"postgresql_version": "PostgreSQL 16.8, compiled by Visual C++ build 1942, 64-bit", "users_count": 4, "organizations_count": 1, "organization_members_count": 4, "projects_count": 1, "boards_count": 1, "columns_count": 4, "cards_count": 4, "checklist_items_count": 0, "foreign_key_constraints": 28}}, "Phase 2": {"tests": [{"name": "Health Endpoint", "status": "FAIL", "details": "Cannot connect to host localhost:3001 ssl:default [The remote computer refused the network connection]"}, {"name": "User Registration", "status": "FAIL", "details": "Cannot connect to host localhost:3001 ssl:default [The remote computer refused the network connection]"}], "score": 0.0, "max_score": 100, "critical_issues": ["Health endpoint error: Cannot connect to host localhost:3001 ssl:default [The remote computer refused the network connection]", "Registration error: Cannot connect to host localhost:3001 ssl:default [The remote computer refused the network connection]"], "live_data_verified": [], "api_responses": {}}, "Phase 3": {"tests": [{"name": "Frontend Accessibility", "status": "FAIL", "details": "Cannot connect to host localhost:3000 ssl:default [The remote computer refused the network connection]"}], "score": 0.0, "max_score": 100, "critical_issues": ["Frontend connection failed: Cannot connect to host localhost:3000 ssl:default [The remote computer refused the network connection]"], "live_data_verified": [], "frontend_stats": {}}, "Phase 4": {"tests": [{"name": "Domain Validation Concept", "status": "PASS", "details": "Valid domains: ['agnoshin.com', 'agno.com'], Invalid domains: ['gmail.com', 'yahoo.com']"}], "score": 100.0, "max_score": 100, "critical_issues": [], "live_data_verified": ["Domain validation concept verified"], "invitation_tests": {}}, "Phase 5": {"tests": [{"name": "Organization Members Table", "status": "PASS", "details": "Table exists with 4 members"}], "score": 100.0, "max_score": 100, "critical_issues": [], "live_data_verified": ["RBAC structure verified: 4 organization members"], "rbac_stats": {}}, "Phase 6": {"tests": [{"name": "Organizations Table", "status": "PASS", "details": "Found 1 organizations"}], "score": 100.0, "max_score": 100, "critical_issues": [], "live_data_verified": ["Multi-tenant structure: 1 organizations"], "multitenant_stats": {}}, "Phase 7": {"tests": [{"name": "API Response Time", "status": "FAIL", "details": "Cannot connect to host localhost:3001 ssl:default [The remote computer refused the network connection]"}, {"name": "Database Query Performance", "status": "PASS", "details": "Query time: 4.0ms for 4 users"}], "score": 50.0, "max_score": 100, "critical_issues": ["Performance test failed: Cannot connect to host localhost:3001 ssl:default [The remote computer refused the network connection]"], "live_data_verified": ["Database performance verified: 4.0ms"], "performance_stats": {"db_query_time_ms": 3.9644241333007812, "user_count": 4}}, "Phase 8": {"tests": [{"name": "Data Persistence", "status": "FAIL", "details": "null value in column \"email_verified\" of relation \"users\" violates not-null constraint\nDETAIL:  Failing row contains (ef6b6ed3-bd79-4168-bef3-755e36e92bf6, <EMAIL>, test_hash, Persistence, Test, null, null, null, null, null, 2025-08-05 21:21:03.399637+05:30, 2025-08-05 21:21:03.399637+05:30)."}], "score": 0.0, "max_score": 100, "critical_issues": ["Data persistence test failed: null value in column \"email_verified\" of relation \"users\" violates not-null constraint\nDETAIL:  Failing row contains (ef6b6ed3-bd79-4168-bef3-755e36e92bf6, <EMAIL>, test_hash, Persistence, Test, null, null, null, null, null, 2025-08-05 21:21:03.399637+05:30, 2025-08-05 21:21:03.399637+05:30)."], "live_data_verified": [], "realtime_stats": {}}}, "overall_score": 53.125, "critical_issues": [], "live_data_validation": {}, "test_data_created": {}}