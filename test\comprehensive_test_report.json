{"test_summary": {"total_tests": 4, "passed": 3, "failed": 1, "success_rate": 75.0, "test_date": "2025-08-05T21:44:04.218589"}, "performance_metrics": {"user_journey": {"total_journey_time": "8.63s", "registration_time": "2.18s", "login_time": "2.15s", "org_access_time": "2.15s", "project_access_time": "2.14s"}, "api_performance": {"/health": {"avg_time": "2.186s", "max_time": "2.223s", "min_time": "2.143s", "meets_target": false}, "/api/organizations": {"avg_time": "2.193s", "max_time": "2.223s", "min_time": "2.165s", "meets_target": false}, "/api/projects": {"avg_time": "2.190s", "max_time": "2.214s", "min_time": "2.164s", "meets_target": false}}}, "accessibility_results": {"color_contrast": {"status": "pass", "ratio": "4.5:1", "wcag_aa": true}, "keyboard_navigation": {"status": "pass", "all_interactive_accessible": true}, "screen_reader": {"status": "pass", "aria_labels_present": true}, "focus_indicators": {"status": "pass", "visible_focus": true}, "semantic_html": {"status": "pass", "proper_headings": true}, "alt_text": {"status": "pass", "images_have_alt": true}}, "detailed_results": [{"test": "End-to-End User Journey", "success": true, "details": "Complete user flow from registration to project access", "timestamp": "2025-08-05T21:43:29.345057", "metrics": {"total_journey_time": "8.63s", "registration_time": "2.18s", "login_time": "2.15s", "org_access_time": "2.15s", "project_access_time": "2.14s"}}, {"test": "API Performance Testing", "success": false, "details": "Tested 3 endpoints", "timestamp": "2025-08-05T21:44:02.694433", "metrics": {"/health": {"avg_time": "2.186s", "max_time": "2.223s", "min_time": "2.143s", "meets_target": false}, "/api/organizations": {"avg_time": "2.193s", "max_time": "2.223s", "min_time": "2.165s", "meets_target": false}, "/api/projects": {"avg_time": "2.190s", "max_time": "2.214s", "min_time": "2.164s", "meets_target": false}}}, {"test": "Accessibility Compliance", "success": true, "details": "WCAG 2.1 AA compliance testing", "timestamp": "2025-08-05T21:44:03.201728", "metrics": {"color_contrast": {"status": "pass", "ratio": "4.5:1", "wcag_aa": true}, "keyboard_navigation": {"status": "pass", "all_interactive_accessible": true}, "screen_reader": {"status": "pass", "aria_labels_present": true}, "focus_indicators": {"status": "pass", "visible_focus": true}, "semantic_html": {"status": "pass", "proper_headings": true}, "alt_text": {"status": "pass", "images_have_alt": true}}}, {"test": "Security Validation", "success": true, "details": "Security measures: 8, Input validation: 3/3", "timestamp": "2025-08-05T21:44:03.705760"}]}