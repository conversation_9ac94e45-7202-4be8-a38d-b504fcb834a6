#!/usr/bin/env python3
"""
Populate Demo Data Script
Manually populate the test database with comprehensive demo data
"""
import asyncio
import asyncpg
import uuid
import json
import hashlib
from datetime import datetime

DATABASE_URL = "postgresql://postgres:admin@localhost:5432/test_agnoworksphere"

def hash_password(password: str) -> str:
    return hashlib.sha256(password.encode()).hexdigest()

async def populate_demo_data():
    """Populate comprehensive demo data"""
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        print("🚀 Populating comprehensive demo data...")
        
        # Clear existing data
        await conn.execute("DELETE FROM card_assignments")
        await conn.execute("DELETE FROM checklist_items")
        await conn.execute("DELETE FROM cards")
        await conn.execute("DELETE FROM columns")
        await conn.execute("DELETE FROM boards")
        await conn.execute("DELETE FROM ai_generated_projects")
        await conn.execute("DELETE FROM projects")
        await conn.execute("DELETE FROM organization_members")
        await conn.execute("DELETE FROM organizations")
        await conn.execute("DELETE FROM users")
        
        print("✅ Cleared existing data")
        
        # Create organization
        org_id = str(uuid.uuid4())
        await conn.execute("""
            INSERT INTO organizations (id, name, description, domain)
            VALUES ($1, $2, $3, $4)
        """, org_id, "ACME Corporation", "Demo organization for testing", "acme.com")
        
        print("✅ Created organization")
        
        # Create users
        demo_users = [
            {"email": "<EMAIL>", "password": "Owner123!", "first_name": "John", "last_name": "Owner", "role": "owner"},
            {"email": "<EMAIL>", "password": "Admin123!", "first_name": "Jane", "last_name": "Admin", "role": "admin"},
            {"email": "<EMAIL>", "password": "Member123!", "first_name": "Bob", "last_name": "Member", "role": "member"},
            {"email": "<EMAIL>", "password": "Viewer123!", "first_name": "Alice", "last_name": "Viewer", "role": "viewer"}
        ]
        
        user_ids = []
        for user_data in demo_users:
            user_id = str(uuid.uuid4())
            user_ids.append(user_id)
            hashed_password = hash_password(user_data["password"])
            
            # Insert user
            await conn.execute("""
                INSERT INTO users (id, email, password_hash, first_name, last_name, email_verified)
                VALUES ($1, $2, $3, $4, $5, $6)
            """, user_id, user_data["email"], hashed_password, user_data["first_name"], 
                user_data["last_name"], True)
            
            # Add to organization
            await conn.execute("""
                INSERT INTO organization_members (user_id, organization_id, role)
                VALUES ($1, $2, $3)
            """, user_id, org_id, user_data["role"])
        
        print(f"✅ Created {len(user_ids)} users")
        
        # Create projects
        projects_data = [
            {
                "name": "Website Redesign",
                "description": "Complete redesign of company website with modern UI/UX",
                "status": "active",
                "priority": "high",
                "progress": 65
            },
            {
                "name": "Mobile App Development", 
                "description": "Native mobile app for iOS and Android platforms",
                "status": "active",
                "priority": "medium",
                "progress": 30
            },
            {
                "name": "Marketing Campaign",
                "description": "Q4 marketing campaign for product launch",
                "status": "active", 
                "priority": "urgent",
                "progress": 80
            }
        ]
        
        project_ids = []
        for i, project_data in enumerate(projects_data):
            project_id = str(uuid.uuid4())
            project_ids.append(project_id)
            
            # Create project
            await conn.execute("""
                INSERT INTO projects (id, name, description, organization_id, status, priority, 
                                    progress, created_by, project_manager)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            """, project_id, project_data["name"], project_data["description"], org_id,
                project_data["status"], project_data["priority"], project_data["progress"],
                user_ids[0], user_ids[1])  # Owner creates, Admin manages
            
            # Create board for project
            board_id = str(uuid.uuid4())
            await conn.execute("""
                INSERT INTO boards (id, name, description, project_id, created_by)
                VALUES ($1, $2, $3, $4, $5)
            """, board_id, f"{project_data['name']} Board", f"Kanban board for {project_data['name']}", 
                project_id, user_ids[0])
            
            # Create columns
            columns_data = [
                {"name": "To Do", "status": "todo", "position": 0},
                {"name": "In Progress", "status": "in_progress", "position": 1},
                {"name": "Review", "status": "review", "position": 2},
                {"name": "Done", "status": "done", "position": 3}
            ]
            
            column_ids = []
            for col_data in columns_data:
                column_id = str(uuid.uuid4())
                column_ids.append(column_id)
                
                await conn.execute("""
                    INSERT INTO columns (id, name, status, position, board_id, created_by)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, column_id, col_data["name"], col_data["status"], col_data["position"],
                    board_id, user_ids[0])
            
            # Create cards for each project
            await create_project_cards(conn, project_id, column_ids, user_ids, i)
        
        print(f"✅ Created {len(project_ids)} projects with boards and columns")
        
        # Create AI projects
        ai_projects_data = [
            {
                "name": "AI-Generated E-commerce Platform",
                "description": "Complete e-commerce solution with AI recommendations",
                "generated_tasks": {
                    "backend_tasks": [
                        "Setup Node.js/Express server",
                        "Implement user authentication",
                        "Create product catalog API",
                        "Integrate payment gateway"
                    ],
                    "frontend_tasks": [
                        "Design responsive UI",
                        "Implement shopping cart",
                        "Create user dashboard",
                        "Add search functionality"
                    ],
                    "ai_features": [
                        "Product recommendation engine",
                        "Chatbot integration",
                        "Inventory prediction",
                        "Price optimization"
                    ]
                }
            },
            {
                "name": "AI-Generated SaaS Dashboard",
                "description": "Analytics dashboard with AI insights",
                "generated_tasks": {
                    "data_tasks": [
                        "Setup data pipeline",
                        "Create data models",
                        "Implement ETL processes",
                        "Setup data warehouse"
                    ],
                    "ai_tasks": [
                        "Predictive analytics",
                        "Anomaly detection",
                        "Automated reporting",
                        "Natural language queries"
                    ],
                    "ui_tasks": [
                        "Interactive charts",
                        "Real-time updates",
                        "Custom dashboards",
                        "Mobile responsiveness"
                    ]
                }
            }
        ]
        
        for ai_project_data in ai_projects_data:
            ai_project_id = str(uuid.uuid4())
            
            await conn.execute("""
                INSERT INTO ai_generated_projects (id, name, description, generated_tasks, 
                                                 organization_id, created_by)
                VALUES ($1, $2, $3, $4, $5, $6)
            """, ai_project_id, ai_project_data["name"], ai_project_data["description"], 
                json.dumps(ai_project_data["generated_tasks"]), org_id, user_ids[0])
        
        print(f"✅ Created {len(ai_projects_data)} AI projects")
        
        await conn.close()
        
        print("\n🎉 Demo data population completed successfully!")
        print("📊 Summary:")
        print(f"   • Users: {len(user_ids)}")
        print(f"   • Organizations: 1")
        print(f"   • Projects: {len(project_ids)}")
        print(f"   • AI Projects: {len(ai_projects_data)}")
        print(f"   • Boards: {len(project_ids)}")
        print(f"   • Columns: {len(project_ids) * 4}")
        print("   • Cards: Multiple per project with assignments and checklists")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to populate demo data: {e}")
        return False

async def create_project_cards(conn, project_id, column_ids, user_ids, project_index):
    """Create demo cards with assignments and checklist items"""
    try:
        # Different cards for each project
        cards_data = [
            # Project 0 - Website Redesign
            [
                {"title": "Design Homepage Mockup", "description": "Create wireframes and mockups for new homepage", "column": 2, "priority": "high", "assigned_to": user_ids[1]},
                {"title": "Implement Responsive Layout", "description": "Code responsive CSS for all screen sizes", "column": 1, "priority": "medium", "assigned_to": user_ids[2]},
                {"title": "User Testing", "description": "Conduct user testing sessions", "column": 0, "priority": "low", "assigned_to": user_ids[3]},
                {"title": "Deploy to Production", "description": "Final deployment and go-live", "column": 3, "priority": "urgent", "assigned_to": user_ids[0]}
            ],
            # Project 1 - Mobile App
            [
                {"title": "Setup Development Environment", "description": "Configure React Native development setup", "column": 3, "priority": "high", "assigned_to": user_ids[1]},
                {"title": "Implement Authentication", "description": "User login and registration functionality", "column": 1, "priority": "high", "assigned_to": user_ids[2]},
                {"title": "Design App Icons", "description": "Create app icons for iOS and Android", "column": 0, "priority": "medium", "assigned_to": user_ids[3]},
                {"title": "API Integration", "description": "Connect app to backend APIs", "column": 0, "priority": "high", "assigned_to": user_ids[1]}
            ],
            # Project 2 - Marketing Campaign  
            [
                {"title": "Social Media Content", "description": "Create content for social media platforms", "column": 2, "priority": "urgent", "assigned_to": user_ids[3]},
                {"title": "Email Campaign Setup", "description": "Configure email marketing automation", "column": 3, "priority": "high", "assigned_to": user_ids[2]},
                {"title": "Landing Page Optimization", "description": "A/B test landing page variations", "column": 1, "priority": "medium", "assigned_to": user_ids[1]},
                {"title": "Analytics Dashboard", "description": "Setup tracking and analytics", "column": 0, "priority": "low", "assigned_to": user_ids[0]}
            ]
        ]
        
        project_cards = cards_data[project_index] if project_index < len(cards_data) else cards_data[0]
        
        for i, card_data in enumerate(project_cards):
            card_id = str(uuid.uuid4())
            
            # Create card
            await conn.execute("""
                INSERT INTO cards (id, title, description, column_id, position, priority, 
                                 created_by, assigned_to)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """, card_id, card_data["title"], card_data["description"], 
                column_ids[card_data["column"]], i, card_data["priority"],
                user_ids[0], card_data["assigned_to"])
            
            # Create card assignment
            await conn.execute("""
                INSERT INTO card_assignments (card_id, user_id)
                VALUES ($1, $2)
            """, card_id, card_data["assigned_to"])
            
            # Create checklist items
            checklist_items = [
                f"Research requirements for {card_data['title']}",
                f"Create initial draft/prototype",
                f"Review and get feedback",
                f"Implement final version"
            ]
            
            for j, item_text in enumerate(checklist_items):
                await conn.execute("""
                    INSERT INTO checklist_items (card_id, text, completed, position)
                    VALUES ($1, $2, $3, $4)
                """, card_id, item_text, j < 2, j)  # First 2 items completed
        
        print(f"✅ Created cards for project {project_index}")
        
    except Exception as e:
        print(f"❌ Failed to create cards for project {project_index}: {e}")

if __name__ == "__main__":
    success = asyncio.run(populate_demo_data())
    exit(0 if success else 1)
