#!/usr/bin/env python3
"""
Fix database schema issues found during comprehensive testing
"""
import asyncio
import asyncpg
from datetime import datetime

async def fix_database_schema():
    """Fix the email_verified column constraint issue"""
    print("🔧 Fixing Database Schema Issues...")
    
    try:
        # Connect to database
        conn = await asyncpg.connect("postgresql://postgres:admin@localhost:5432/agno_worksphere")
        print("✅ Connected to PostgreSQL database")
        
        # Fix email_verified column - add default value
        print("🔧 Fixing email_verified column constraint...")
        
        # First, update existing NULL values to FALSE
        await conn.execute("""
            UPDATE users 
            SET email_verified = FALSE 
            WHERE email_verified IS NULL
        """)
        print("✅ Updated existing NULL email_verified values to FALSE")
        
        # Add default value to the column
        await conn.execute("""
            ALTER TABLE users 
            ALTER COLUMN email_verified SET DEFAULT FALSE
        """)
        print("✅ Set default value for email_verified column")
        
        # Verify the fix
        result = await conn.fetchrow("""
            SELECT column_name, is_nullable, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'email_verified'
        """)
        
        if result:
            print(f"✅ Column verification: {result['column_name']} - Nullable: {result['is_nullable']}, Default: {result['column_default']}")
        
        # Check if there are any other columns with similar issues
        print("\n🔍 Checking for other potential schema issues...")
        
        nullable_columns = await conn.fetch("""
            SELECT table_name, column_name, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND is_nullable = 'NO' 
            AND column_default IS NULL
            AND column_name NOT IN ('id', 'created_at', 'updated_at')
            ORDER BY table_name, column_name
        """)
        
        if nullable_columns:
            print("⚠️ Found other NOT NULL columns without defaults:")
            for col in nullable_columns:
                print(f"  • {col['table_name']}.{col['column_name']}")
        else:
            print("✅ No other schema issues found")
        
        await conn.close()
        print("✅ Database schema fixes completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database schema fix failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_database_schema())
    if success:
        print("\n🎉 Database schema is now ready for testing!")
    else:
        print("\n💥 Database schema fix failed!")
