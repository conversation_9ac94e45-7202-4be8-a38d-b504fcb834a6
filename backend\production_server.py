#!/usr/bin/env python3
"""
Production-Grade FastAPI Server for Agno WorkSphere
Includes connection pooling, caching, monitoring, and optimizations
"""
import asyncio
import logging
import time
import uuid
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional

import asyncpg
try:
    import redis.asyncio as redis
except ImportError:
    import redis
    # For older Redis versions, we'll use a simple fallback
    redis.from_url = redis.Redis.from_url
import uvicorn
from fastapi import FastAPI, HTTPException, Depends, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import structlog

from app.config import settings
from app.database import DatabasePool
from app.cache import cache_manager
from app.monitoring import (
    metrics_collector, health_checker, system_monitor, alert_manager,
    monitoring_middleware, get_prometheus_metrics
)

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Use the enhanced managers from modules
db_pool = DatabasePool()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Agno WorkSphere API", version=settings.app_version)

    # Initialize database pool
    await db_pool.initialize()

    # Initialize Redis cache
    await cache_manager.initialize()

    # Run database optimizations
    await optimize_database()

    logger.info("Application startup completed")

    yield

    # Shutdown
    logger.info("Shutting down Agno WorkSphere API")
    await db_pool.close()
    await cache_manager.close()
    logger.info("Application shutdown completed")

# Create FastAPI app with lifespan
app = FastAPI(
    title="Agno WorkSphere API",
    description="Production-grade project management system API",
    version=settings.app_version,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan
)

# Add middleware
if settings.enable_gzip:
    app.add_middleware(
        GZipMiddleware, 
        minimum_size=settings.gzip_minimum_size
    )

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add trusted host middleware for production
if settings.environment == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*.agno-worksphere.com", "localhost"]
    )

# Add monitoring middleware
app.middleware("http")(monitoring_middleware)

# Exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error("Unhandled exception", 
                 error=str(exc),
                 method=request.method,
                 url=str(request.url),
                 exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "request_id": request.headers.get("X-Request-ID"),
            "timestamp": time.time()
        }
    )

async def optimize_database():
    """Run database optimizations on startup"""
    try:
        from database_optimization import DatabaseOptimizer
        optimizer = DatabaseOptimizer(settings.database_url)
        result = await optimizer.run_full_optimization()
        if result["success"]:
            logger.info("Database optimizations completed successfully")
        else:
            logger.error("Database optimization failed", error=result.get("error"))
    except Exception as e:
        logger.error("Database optimization failed", error=str(e))

# Dependency to get database connection
async def get_db():
    """Dependency to get database connection"""
    async with db_pool.pool.acquire() as conn:
        yield conn

# Dependency to get cache
async def get_cache():
    """Dependency to get cache manager"""
    return cache_manager

# Pydantic models
class UserRegister(BaseModel):
    email: str
    password: str
    first_name: str
    last_name: str
    organization_name: Optional[str] = None
    organization_domain: Optional[str] = None

class UserLogin(BaseModel):
    email: str
    password: str

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    database: str
    cache: str
    version: str
    uptime: float

# API Routes
@app.get("/health", response_model=HealthResponse)
async def health_check(
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Enhanced health check with database and cache status"""
    start_time = time.time()

    # Check database
    try:
        await db.fetchval("SELECT 1")
        db_status = "connected"
    except Exception as e:
        logger.error("Database health check failed", error=str(e))
        db_status = f"error: {str(e)}"

    # Check cache
    try:
        if cache.redis:
            await cache.redis.ping()
            cache_status = "connected"
        else:
            cache_status = "disabled"
    except Exception as e:
        logger.error("Cache health check failed", error=str(e))
        cache_status = f"error: {str(e)}"

    return HealthResponse(
        status="healthy" if db_status == "connected" else "degraded",
        timestamp=time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
        database=db_status,
        cache=cache_status,
        version=settings.app_version,
        uptime=time.time() - start_time
    )

@app.post("/api/v1/auth/register")
async def register_user(
    user_data: UserRegister,
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Register a new user with caching"""
    try:
        # Check cache first
        cache_key = f"user_exists:{user_data.email}"
        cached_result = await cache.get(cache_key)

        if cached_result == "exists":
            raise HTTPException(status_code=400, detail="User already exists")

        # Check if user exists in database
        existing = await db.fetchrow(
            "SELECT id FROM users WHERE email = $1", user_data.email
        )

        if existing:
            # Cache the result
            await cache.set(cache_key, "exists", ttl=3600)
            raise HTTPException(status_code=400, detail="User already exists")

        # Generate UUID for user
        user_uuid = str(uuid.uuid4())

        # Create user
        user_id = await db.fetchval("""
            INSERT INTO users (id, email, password_hash, first_name, last_name,
                             email_verified, two_factor_enabled, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
            RETURNING id
        """, user_uuid, user_data.email, "hashed_" + user_data.password,
            user_data.first_name, user_data.last_name, False, False)

        # Invalidate cache
        await cache.delete(cache_key)

        logger.info("User registered successfully", user_id=str(user_id), email=user_data.email)

        return {
            "success": True,
            "message": "User registered successfully",
            "user_id": str(user_id),
            "email": user_data.email
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("User registration failed", error=str(e), email=user_data.email)
        raise HTTPException(status_code=500, detail=f"Registration failed: {str(e)}")

@app.post("/api/v1/auth/login")
async def login_user(
    login_data: UserLogin,
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Login user with caching"""
    try:
        # Check cache first
        cache_key = f"user_login:{login_data.email}"
        cached_user = await cache.get(cache_key)

        if not cached_user:
            # Get from database
            user = await db.fetchrow(
                "SELECT id, email, first_name, last_name FROM users WHERE email = $1",
                login_data.email
            )

            if not user:
                raise HTTPException(status_code=401, detail="Invalid credentials")

            # Cache user data
            import json
            user_data = dict(user)
            await cache.set(cache_key, json.dumps(user_data, default=str), ttl=1800)
        else:
            import json
            user_data = json.loads(cached_user)
            user = user_data

        # Generate token
        token = f"token_{user['id']}"

        logger.info("User logged in successfully", user_id=str(user['id']), email=user['email'])

        return {
            "success": True,
            "token": token,
            "user": {
                "id": str(user['id']),
                "email": user['email'],
                "first_name": user['first_name'],
                "last_name": user['last_name'],
                "role": "owner"  # Default for testing
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("User login failed", error=str(e), email=login_data.email)
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@app.get("/api/v1/organizations")
async def get_organizations(
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Get organizations with caching"""
    try:
        cache_key = "organizations:all"
        cached_orgs = await cache.get(cache_key)

        if cached_orgs:
            import json
            organizations = json.loads(cached_orgs)
        else:
            # Get from database
            orgs = await db.fetch(
                "SELECT id, name, description, created_at FROM organizations ORDER BY created_at DESC"
            )

            organizations = [dict(org) for org in orgs]

            # Cache results
            import json
            await cache.set(cache_key, json.dumps(organizations, default=str), ttl=600)

        logger.info("Organizations retrieved", count=len(organizations))

        return {
            "success": True,
            "organizations": organizations
        }

    except Exception as e:
        logger.error("Failed to get organizations", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get organizations: {str(e)}")

@app.get("/api/v1/projects")
async def get_projects(
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Get projects with caching"""
    try:
        cache_key = "projects:all"
        cached_projects = await cache.get(cache_key)

        if cached_projects:
            import json
            projects = json.loads(cached_projects)
        else:
            # Get from database
            project_rows = await db.fetch(
                "SELECT id, name, description, status, priority, created_at FROM projects ORDER BY created_at DESC"
            )

            projects = [dict(project) for project in project_rows]

            # Cache results
            import json
            await cache.set(cache_key, json.dumps(projects, default=str), ttl=300)

        logger.info("Projects retrieved", count=len(projects))

        return {
            "success": True,
            "projects": projects
        }

    except Exception as e:
        logger.error("Failed to get projects", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get projects: {str(e)}")

# Add boards endpoints
@app.get("/api/v1/boards")
async def get_boards(
    project_id: str = None,
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Get boards, optionally filtered by project"""
    try:
        if project_id:
            cache_key = f"boards:project:{project_id}"
            query = "SELECT id, name, description, project_id, created_at FROM boards WHERE project_id = $1 ORDER BY created_at DESC"
            params = [project_id]
        else:
            cache_key = "boards:all"
            query = "SELECT id, name, description, project_id, created_at FROM boards ORDER BY created_at DESC"
            params = []

        cached_boards = await cache.get(cache_key)

        if cached_boards:
            import json
            boards = json.loads(cached_boards)
        else:
            # Get from database
            if params:
                board_rows = await db.fetch(query, *params)
            else:
                board_rows = await db.fetch(query)

            boards = [dict(board) for board in board_rows]

            # Cache results
            import json
            await cache.set(cache_key, json.dumps(boards, default=str), ttl=300)

        logger.info("Boards retrieved", count=len(boards), project_id=project_id)

        return {
            "success": True,
            "data": boards
        }

    except Exception as e:
        logger.error("Failed to get boards", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get boards: {str(e)}")

# Add columns endpoints
@app.get("/api/v1/columns")
async def get_columns(
    board_id: str = None,
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Get columns, optionally filtered by board"""
    try:
        if board_id:
            cache_key = f"columns:board:{board_id}"
            query = "SELECT id, name, position, board_id, created_at FROM columns WHERE board_id = $1 ORDER BY position"
            params = [board_id]
        else:
            cache_key = "columns:all"
            query = "SELECT id, name, position, board_id, created_at FROM columns ORDER BY board_id, position"
            params = []

        cached_columns = await cache.get(cache_key)

        if cached_columns:
            import json
            columns = json.loads(cached_columns)
        else:
            # Get from database
            if params:
                column_rows = await db.fetch(query, *params)
            else:
                column_rows = await db.fetch(query)

            columns = [dict(column) for column in column_rows]

            # Cache results
            import json
            await cache.set(cache_key, json.dumps(columns, default=str), ttl=300)

        logger.info("Columns retrieved", count=len(columns), board_id=board_id)

        return {
            "success": True,
            "data": columns
        }

    except Exception as e:
        logger.error("Failed to get columns", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get columns: {str(e)}")

# Add cards endpoints
@app.get("/api/v1/cards")
async def get_cards(
    column_id: str = None,
    board_id: str = None,
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Get cards, optionally filtered by column or board"""
    try:
        if column_id:
            cache_key = f"cards:column:{column_id}"
            query = """
                SELECT c.id, c.title, c.description, c.position, c.priority,
                       c.due_date, c.column_id, c.created_by, c.created_at, c.updated_at
                FROM cards c
                WHERE c.column_id = $1
                ORDER BY c.position
            """
            params = [column_id]
        elif board_id:
            cache_key = f"cards:board:{board_id}"
            query = """
                SELECT c.id, c.title, c.description, c.position, c.priority,
                       c.due_date, c.column_id, c.created_by, c.created_at, c.updated_at
                FROM cards c
                JOIN columns col ON c.column_id = col.id
                WHERE col.board_id = $1
                ORDER BY col.position, c.position
            """
            params = [board_id]
        else:
            cache_key = "cards:all"
            query = """
                SELECT c.id, c.title, c.description, c.position, c.priority,
                       c.due_date, c.column_id, c.created_by, c.created_at, c.updated_at
                FROM cards c
                ORDER BY c.created_at DESC
            """
            params = []

        cached_cards = await cache.get(cache_key)

        if cached_cards:
            import json
            cards = json.loads(cached_cards)
        else:
            # Get from database
            if params:
                card_rows = await db.fetch(query, *params)
            else:
                card_rows = await db.fetch(query)

            cards = []
            for card in card_rows:
                card_data = dict(card)

                # Get assignees for this card
                assignees = await db.fetch("""
                    SELECT u.id, u.email, u.first_name, u.last_name, u.avatar_url
                    FROM card_assignments ca
                    JOIN users u ON ca.user_id = u.id
                    WHERE ca.card_id = $1
                """, card["id"])

                card_data["assignees"] = [dict(assignee) for assignee in assignees]

                # Get checklist items for this card
                checklist_items = await db.fetch("""
                    SELECT id, text, completed, position
                    FROM checklist_items
                    WHERE card_id = $1
                    ORDER BY position
                """, card["id"])

                card_data["checklist"] = [dict(item) for item in checklist_items]

                cards.append(card_data)

            # Cache results
            import json
            await cache.set(cache_key, json.dumps(cards, default=str), ttl=300)

        logger.info("Cards retrieved", count=len(cards), column_id=column_id, board_id=board_id)

        return {
            "success": True,
            "data": cards
        }

    except Exception as e:
        logger.error("Failed to get cards", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get cards: {str(e)}")

@app.post("/api/v1/cards")
async def create_card(
    card_data: dict,
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Create a new card"""
    try:
        # Generate UUID for card
        card_uuid = str(uuid.uuid4())

        # Create card
        card_id = await db.fetchval("""
            INSERT INTO cards (id, title, description, column_id, position, priority,
                             due_date, created_by, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
            RETURNING id
        """, card_uuid, card_data.get("title"), card_data.get("description"),
            card_data.get("column_id"), card_data.get("position", 0),
            card_data.get("priority", "medium"), card_data.get("due_date"),
            "user-1")  # Default user for now

        # Add assignees if provided
        if card_data.get("assigned_to"):
            for user_id in card_data["assigned_to"]:
                await db.execute("""
                    INSERT INTO card_assignments (id, card_id, user_id, assigned_at)
                    VALUES ($1, $2, $3, NOW())
                """, str(uuid.uuid4()), card_id, user_id)

        # Add checklist items if provided
        if card_data.get("checklist"):
            for i, item in enumerate(card_data["checklist"]):
                await db.execute("""
                    INSERT INTO checklist_items (id, card_id, text, completed, position, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
                """, str(uuid.uuid4()), card_id, item.get("text", ""),
                    item.get("completed", False), i)

        # Invalidate cache
        await cache.delete(f"cards:column:{card_data.get('column_id')}")
        await cache.delete("cards:all")

        logger.info("Card created successfully", card_id=str(card_id))

        return {
            "success": True,
            "data": {
                "id": str(card_id),
                "message": "Card created successfully"
            }
        }

    except Exception as e:
        logger.error("Card creation failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Card creation failed: {str(e)}")

@app.get("/metrics")
async def get_metrics(
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Get application metrics"""
    try:
        # Database metrics
        db_stats = await db.fetchrow("""
            SELECT
                (SELECT COUNT(*) FROM users) as user_count,
                (SELECT COUNT(*) FROM organizations) as org_count,
                (SELECT COUNT(*) FROM projects) as project_count,
                (SELECT COUNT(*) FROM cards) as task_count
        """)

        # Cache metrics
        cache_info = {
            "connected": cache.redis is not None,
            "status": "healthy" if cache.redis else "disabled"
        }

        if cache.redis:
            try:
                cache_info.update(await cache.redis.info())
            except:
                cache_info["status"] = "error"

        return {
            "database": dict(db_stats),
            "cache": cache_info,
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        }

    except Exception as e:
        logger.error("Failed to get metrics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")

@app.get("/health/detailed")
async def detailed_health_check():
    """Comprehensive health check with all system components"""
    return await health_checker.run_all_checks()

@app.get("/metrics/prometheus")
async def prometheus_metrics():
    """Prometheus metrics endpoint"""
    return await get_prometheus_metrics()

@app.get("/metrics/system")
async def system_metrics():
    """System resource metrics"""
    return system_monitor.get_system_metrics()

# Add the missing /api/users/me endpoint
@app.get("/api/v1/users/me")
async def get_current_user_me(
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Get current user profile - /api/users/me endpoint"""
    try:
        # For now, return a mock user since we don't have proper auth in production server
        # In a real implementation, this would get the user from the JWT token
        user_data = {
            "id": "user-1",
            "email": "<EMAIL>",
            "first_name": "Demo",
            "last_name": "User",
            "avatar_url": None,
            "email_verified": True,
            "two_factor_enabled": False,
            "role": "owner",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }

        logger.info("Current user profile retrieved", user_id=user_data["id"])

        return {
            "success": True,
            "data": user_data
        }

    except Exception as e:
        logger.error("Failed to get current user", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get current user: {str(e)}")

# Add comprehensive boards endpoint with cards and assignees
@app.get("/api/v1/boards/{board_id}")
async def get_board_with_details(
    board_id: str,
    db: asyncpg.Connection = Depends(get_db),
    cache: CacheManager = Depends(get_cache)
):
    """Get board with columns, cards, and assignees"""
    try:
        cache_key = f"board_details:{board_id}"
        cached_board = await cache.get(cache_key)

        if cached_board:
            import json
            board_data = json.loads(cached_board)
        else:
            # Get board details from database
            board = await db.fetchrow(
                "SELECT id, name, description, project_id, created_at FROM boards WHERE id = $1",
                board_id
            )

            if not board:
                raise HTTPException(status_code=404, detail="Board not found")

            # Get columns for this board
            columns = await db.fetch(
                "SELECT id, name, position, board_id, created_at FROM columns WHERE board_id = $1 ORDER BY position",
                board_id
            )

            # Get cards for each column with assignees
            board_data = dict(board)
            board_data["columns"] = []

            for column in columns:
                column_data = dict(column)

                # Get cards for this column
                cards = await db.fetch("""
                    SELECT c.id, c.title, c.description, c.position, c.priority,
                           c.due_date, c.created_by, c.created_at, c.updated_at
                    FROM cards c
                    WHERE c.column_id = $1
                    ORDER BY c.position
                """, column["id"])

                column_data["cards"] = []

                for card in cards:
                    card_data = dict(card)

                    # Get assignees for this card
                    assignees = await db.fetch("""
                        SELECT u.id, u.email, u.first_name, u.last_name, u.avatar_url
                        FROM card_assignments ca
                        JOIN users u ON ca.user_id = u.id
                        WHERE ca.card_id = $1
                    """, card["id"])

                    card_data["assignees"] = [dict(assignee) for assignee in assignees]

                    # Get checklist items for this card
                    checklist_items = await db.fetch("""
                        SELECT id, text, completed, position
                        FROM checklist_items
                        WHERE card_id = $1
                        ORDER BY position
                    """, card["id"])

                    card_data["checklist"] = [dict(item) for item in checklist_items]

                    column_data["cards"].append(card_data)

                board_data["columns"].append(column_data)

            # Cache the result
            import json
            await cache.set(cache_key, json.dumps(board_data, default=str), ttl=300)

        logger.info("Board details retrieved", board_id=board_id, columns_count=len(board_data.get("columns", [])))

        return {
            "success": True,
            "data": board_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get board details", error=str(e), board_id=board_id)
        raise HTTPException(status_code=500, detail=f"Failed to get board details: {str(e)}")

@app.get("/metrics/application")
async def application_metrics():
    """Application-specific metrics"""
    return {
        "requests": metrics_collector.get_request_stats(),
        "database": await db_pool.get_stats(),
        "cache": await cache_manager.get_stats()
    }

if __name__ == "__main__":
    uvicorn.run(
        "production_server:app",
        host="0.0.0.0",
        port=3001,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True,
        workers=1 if settings.debug else 4
    )
