#!/usr/bin/env python3
"""
Quick database structure check
"""
import asyncio
import asyncpg
import json

async def check_database():
    conn = await asyncpg.connect('postgresql://postgres:admin@localhost:5432/test_agnoworksphere')
    
    print("🤖 AI Projects JSONB Data:")
    rows = await conn.fetch('SELECT id, name, generated_tasks FROM ai_generated_projects LIMIT 2')
    for row in rows:
        print(f"  {row['name']}: {type(row['generated_tasks'])}")
        if row['generated_tasks']:
            print(f"    Content: {str(row['generated_tasks'])[:200]}...")
    
    print("\n🏢 Organizations Schema:")
    schema = await conn.fetch("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'organizations'
        ORDER BY ordinal_position
    """)
    for col in schema:
        print(f"  {col['column_name']}: {col['data_type']}")
    
    print("\n👥 Users Schema:")
    user_schema = await conn.fetch("""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'users'
        ORDER BY ordinal_position
    """)
    for col in user_schema:
        print(f"  {col['column_name']}: {col['data_type']}")
    
    await conn.close()

if __name__ == "__main__":
    asyncio.run(check_database())
