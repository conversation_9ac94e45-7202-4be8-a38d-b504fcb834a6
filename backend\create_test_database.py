#!/usr/bin/env python3
"""
Create test database and schema for production-ready enhanced server
"""
import asyncio
import asyncpg
import sys
import os

# Database configuration
MAIN_DATABASE_URL = "postgresql://postgres:admin@localhost:5432/agno_worksphere"
TEST_DATABASE_URL = "postgresql://postgres:admin@localhost:5432/test_agnoworksphere"
POSTGRES_URL = "postgresql://postgres:admin@localhost:5432/postgres"

async def create_test_database():
    """Create the test database if it doesn't exist"""
    try:
        # Connect to postgres database to create new database
        conn = await asyncpg.connect(POSTGRES_URL)
        
        # Check if test database exists
        exists = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = 'test_agnoworksphere'"
        )
        
        if not exists:
            # Create the test database
            await conn.execute("CREATE DATABASE test_agnoworksphere")
            print("✅ Created test_agnoworksphere database")
        else:
            print("✅ test_agnoworksphere database already exists")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to create test database: {e}")
        return False

async def copy_schema_from_main_database():
    """Copy schema from main database to test database"""
    try:
        # Connect to main database to get schema
        main_conn = await asyncpg.connect(MAIN_DATABASE_URL)
        
        # Get all table creation statements
        tables_query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
        """
        
        tables = await main_conn.fetch(tables_query)
        print(f"📋 Found {len(tables)} tables to copy")
        
        # Connect to test database
        test_conn = await asyncpg.connect(TEST_DATABASE_URL)
        
        # Copy each table structure
        for table in tables:
            table_name = table['table_name']
            
            # Get table creation SQL
            create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} AS 
            SELECT * FROM dblink('host=localhost port=5432 dbname=agno_worksphere user=postgres password=admin',
                                 'SELECT * FROM {table_name} LIMIT 0') 
            AS t({await get_table_columns(main_conn, table_name)})
            """
            
            # Simpler approach - get table structure and recreate
            columns_info = await main_conn.fetch(f"""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = '{table_name}' 
                AND table_schema = 'public'
                ORDER BY ordinal_position
            """)
            
            if columns_info:
                # Build CREATE TABLE statement
                column_defs = []
                for col in columns_info:
                    col_def = f"{col['column_name']} {col['data_type']}"
                    if col['is_nullable'] == 'NO':
                        col_def += " NOT NULL"
                    if col['column_default']:
                        col_def += f" DEFAULT {col['column_default']}"
                    column_defs.append(col_def)
                
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    {', '.join(column_defs)}
                )
                """
                
                try:
                    await test_conn.execute(create_table_sql)
                    print(f"✅ Created table: {table_name}")
                except Exception as e:
                    print(f"⚠️ Error creating table {table_name}: {e}")
        
        await main_conn.close()
        await test_conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to copy schema: {e}")
        return False

async def get_table_columns(conn, table_name):
    """Get column definitions for a table"""
    columns = await conn.fetch(f"""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = '{table_name}' 
        AND table_schema = 'public'
        ORDER BY ordinal_position
    """)
    
    return ', '.join([f"{col['column_name']} {col['data_type']}" for col in columns])

async def create_enhanced_schema():
    """Create the enhanced schema with proper tables"""
    try:
        conn = await asyncpg.connect(TEST_DATABASE_URL)
        
        # Create tables with proper schema
        tables_sql = [
            # Users table
            """
            CREATE TABLE IF NOT EXISTS users (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                first_name VARCHAR(100) NOT NULL,
                last_name VARCHAR(100),
                email_verified BOOLEAN DEFAULT FALSE,
                two_factor_enabled BOOLEAN DEFAULT FALSE,
                avatar_url TEXT,
                last_login_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
            """,
            
            # Organizations table
            """
            CREATE TABLE IF NOT EXISTS organizations (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(255) NOT NULL,
                description TEXT,
                domain VARCHAR(255),
                organization_type VARCHAR(50) DEFAULT 'business',
                language VARCHAR(10) DEFAULT 'en',
                timezone VARCHAR(50) DEFAULT 'UTC',
                allow_cross_org_collaboration BOOLEAN DEFAULT TRUE,
                created_by UUID REFERENCES users(id),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
            """,
            
            # Organization members table
            """
            CREATE TABLE IF NOT EXISTS organization_members (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
                role VARCHAR(20) NOT NULL CHECK (role IN ('owner', 'admin', 'member', 'viewer')),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW(),
                UNIQUE(user_id, organization_id)
            )
            """,
            
            # Projects table
            """
            CREATE TABLE IF NOT EXISTS projects (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(255) NOT NULL,
                description TEXT,
                organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
                status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived', 'on_hold')),
                priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
                start_date DATE,
                end_date DATE,
                budget DECIMAL(15,2),
                progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
                created_by UUID REFERENCES users(id),
                project_manager UUID REFERENCES users(id),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
            """,
            
            # AI Generated Projects table (new requirement)
            """
            CREATE TABLE IF NOT EXISTS ai_generated_projects (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(255) NOT NULL,
                description TEXT,
                generated_tasks JSONB,
                organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
                created_by UUID REFERENCES users(id),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
            """,
            
            # Boards table
            """
            CREATE TABLE IF NOT EXISTS boards (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(255) NOT NULL,
                description TEXT,
                project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
                created_by UUID REFERENCES users(id),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
            """,
            
            # Columns table
            """
            CREATE TABLE IF NOT EXISTS columns (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(255) NOT NULL,
                status VARCHAR(50),
                position INTEGER DEFAULT 0,
                board_id UUID REFERENCES boards(id) ON DELETE CASCADE,
                created_by UUID REFERENCES users(id),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
            """,
            
            # Cards table
            """
            CREATE TABLE IF NOT EXISTS cards (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                title VARCHAR(255) NOT NULL,
                description TEXT,
                column_id UUID REFERENCES columns(id) ON DELETE CASCADE,
                position INTEGER DEFAULT 0,
                priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
                due_date TIMESTAMP,
                created_by UUID REFERENCES users(id),
                assigned_to UUID REFERENCES users(id),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
            """,
            
            # Checklist items table
            """
            CREATE TABLE IF NOT EXISTS checklist_items (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                card_id UUID REFERENCES cards(id) ON DELETE CASCADE,
                text TEXT NOT NULL,
                completed BOOLEAN DEFAULT FALSE,
                position INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
            """,
            
            # Card assignments table
            """
            CREATE TABLE IF NOT EXISTS card_assignments (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                card_id UUID REFERENCES cards(id) ON DELETE CASCADE,
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                assigned_at TIMESTAMP DEFAULT NOW(),
                UNIQUE(card_id, user_id)
            )
            """,
            
            # Sessions table
            """
            CREATE TABLE IF NOT EXISTS sessions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                token_hash VARCHAR(255) NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT NOW()
            )
            """,
            
            # User preferences table
            """
            CREATE TABLE IF NOT EXISTS user_preferences (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
                theme VARCHAR(20) DEFAULT 'light',
                language VARCHAR(10) DEFAULT 'en',
                timezone VARCHAR(50) DEFAULT 'UTC',
                email_notifications BOOLEAN DEFAULT TRUE,
                push_notifications BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
            """,
            
            # Attachments table
            """
            CREATE TABLE IF NOT EXISTS attachments (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                card_id UUID REFERENCES cards(id) ON DELETE CASCADE,
                filename VARCHAR(255) NOT NULL,
                file_size INTEGER,
                file_type VARCHAR(100),
                file_url TEXT,
                uploaded_by UUID REFERENCES users(id),
                uploaded_at TIMESTAMP DEFAULT NOW()
            )
            """
        ]
        
        # Execute all table creation statements
        for i, sql in enumerate(tables_sql, 1):
            try:
                await conn.execute(sql)
                table_name = sql.split("CREATE TABLE IF NOT EXISTS ")[1].split(" (")[0]
                print(f"✅ Created table {i}/{len(tables_sql)}: {table_name}")
            except Exception as e:
                print(f"❌ Error creating table {i}: {e}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to create enhanced schema: {e}")
        return False

async def create_indexes():
    """Create essential indexes for performance"""
    try:
        conn = await asyncpg.connect(TEST_DATABASE_URL)
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_organization_members_user_id ON organization_members(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_organization_members_org_id ON organization_members(organization_id)",
            "CREATE INDEX IF NOT EXISTS idx_projects_organization_id ON projects(organization_id)",
            "CREATE INDEX IF NOT EXISTS idx_projects_created_by ON projects(created_by)",
            "CREATE INDEX IF NOT EXISTS idx_ai_generated_projects_org_id ON ai_generated_projects(organization_id)",
            "CREATE INDEX IF NOT EXISTS idx_ai_generated_projects_created_by ON ai_generated_projects(created_by)",
            "CREATE INDEX IF NOT EXISTS idx_boards_project_id ON boards(project_id)",
            "CREATE INDEX IF NOT EXISTS idx_columns_board_id ON columns(board_id)",
            "CREATE INDEX IF NOT EXISTS idx_cards_column_id ON cards(column_id)",
            "CREATE INDEX IF NOT EXISTS idx_checklist_items_card_id ON checklist_items(card_id)",
            "CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_sessions_active ON sessions(is_active)"
        ]
        
        for i, index_sql in enumerate(indexes, 1):
            try:
                await conn.execute(index_sql)
                print(f"✅ Created index {i}/{len(indexes)}")
            except Exception as e:
                print(f"⚠️ Index {i} already exists or failed: {e}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to create indexes: {e}")
        return False

async def main():
    """Main function to set up test database"""
    print("🚀 Setting up test database for production-ready enhanced server")
    print("=" * 70)
    
    # Step 1: Create test database
    print("\n📋 Step 1: Creating test database...")
    if not await create_test_database():
        print("❌ Failed to create test database")
        return False
    
    # Step 2: Create enhanced schema
    print("\n📋 Step 2: Creating enhanced schema...")
    if not await create_enhanced_schema():
        print("❌ Failed to create enhanced schema")
        return False
    
    # Step 3: Create indexes
    print("\n📋 Step 3: Creating performance indexes...")
    if not await create_indexes():
        print("❌ Failed to create indexes")
        return False
    
    print("\n🎉 Test database setup completed successfully!")
    print(f"📍 Database URL: {TEST_DATABASE_URL}")
    print("✅ Ready for production-ready enhanced server")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
