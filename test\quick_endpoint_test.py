#!/usr/bin/env python3
"""
Quick endpoint test for Agno WorkSphere
"""
import requests
import json

def test_endpoint(method, url, data=None, headers=None):
    """Test a single endpoint"""
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers)
        else:
            return f"❌ Unsupported method: {method}"
        
        status_emoji = "✅" if response.status_code < 400 else "❌"
        return f"{status_emoji} {method} {url} - Status: {response.status_code}"
    except Exception as e:
        return f"❌ {method} {url} - Error: {str(e)}"

def main():
    base_url = "http://localhost:3001/api/v1"
    
    print("🚀 Quick Endpoint Test")
    print("=" * 50)
    
    # Test basic endpoints
    endpoints = [
        ("GET", f"{base_url}/"),
        ("GET", f"{base_url}/auth"),
        ("GET", f"{base_url}/users"),
        ("GET", f"{base_url}/organizations"),
        ("GET", f"{base_url}/projects"),
        ("GET", f"{base_url}/boards"),
        ("GET", f"{base_url}/cards"),
        ("GET", f"{base_url}/checklist"),
        ("GET", f"{base_url}/teams"),
        ("GET", f"{base_url}/analytics"),
        ("GET", f"{base_url}/ai"),
        ("GET", f"{base_url}/ai-projects"),
    ]
    
    for method, url in endpoints:
        result = test_endpoint(method, url)
        print(result)
    
    print("\n🔐 Testing Authentication Endpoints")
    print("-" * 30)
    
    # Test registration
    reg_data = {
        "email": "<EMAIL>",
        "password": "testpass123",
        "full_name": "Test User"
    }
    result = test_endpoint("POST", f"{base_url}/auth/register", reg_data)
    print(result)
    
    # Test login
    login_data = {
        "username": "<EMAIL>",
        "password": "testpass123"
    }
    result = test_endpoint("POST", f"{base_url}/auth/login", login_data)
    print(result)
    
    print("\n📊 Frontend Accessibility Test")
    print("-" * 30)
    
    frontend_url = "http://localhost:3000"
    result = test_endpoint("GET", frontend_url)
    print(result)
    
    print("\n✅ Quick test completed!")

if __name__ == "__main__":
    main()
