#!/usr/bin/env python3
import asyncio
import asyncpg

async def check_database():
    conn = await asyncpg.connect('postgresql://postgres:admin@localhost:5432/test_agnoworksphere')
    
    users = await conn.fetchval('SELECT COUNT(*) FROM users')
    orgs = await conn.fetchval('SELECT COUNT(*) FROM organizations')
    projects = await conn.fetchval('SELECT COUNT(*) FROM projects')
    boards = await conn.fetchval('SELECT COUNT(*) FROM boards')
    columns = await conn.fetchval('SELECT COUNT(*) FROM columns')
    cards = await conn.fetchval('SELECT COUNT(*) FROM cards')
    
    print(f'Users: {users}')
    print(f'Organizations: {orgs}')
    print(f'Projects: {projects}')
    print(f'Boards: {boards}')
    print(f'Columns: {columns}')
    print(f'Cards: {cards}')
    
    await conn.close()

asyncio.run(check_database())
