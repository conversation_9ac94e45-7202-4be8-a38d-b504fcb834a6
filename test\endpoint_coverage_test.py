#!/usr/bin/env python3
"""
Endpoint Coverage Test for Agno WorkSphere
Verifies all required endpoints are implemented and accessible
"""
import requests
import json
from typing import Dict, List, Any

BASE_URL = "http://localhost:3001"

class EndpointCoverageTest:
    def __init__(self):
        self.session = requests.Session()
        self.auth_token = None
        self.coverage_results = {
            "implemented": [],
            "missing": [],
            "errors": []
        }
    
    def authenticate(self):
        """Get authentication token for protected endpoints"""
        try:
            # Try to login with demo user
            login_data = {
                "email": "<EMAIL>",
                "password": "demo123"
            }
            
            response = self.session.post(
                f"{BASE_URL}/api/auth/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if "access_token" in data:
                    self.auth_token = data["access_token"]
                    print(f"✅ Authentication successful")
                    return True
            
            print(f"⚠️ Authentication failed, testing without auth")
            return False
            
        except Exception as e:
            print(f"⚠️ Authentication error: {e}")
            return False
    
    def test_endpoint(self, method: str, endpoint: str, description: str) -> Dict[str, Any]:
        """Test if an endpoint exists and is accessible"""
        try:
            url = f"{BASE_URL}{endpoint}"
            headers = {}
            
            # Add auth header if available
            if self.auth_token:
                headers["Authorization"] = f"Bearer {self.auth_token}"
            
            response = self.session.request(
                method=method,
                url=url,
                headers=headers,
                timeout=10
            )
            
            # Consider endpoint implemented if it doesn't return 404
            if response.status_code != 404:
                self.coverage_results["implemented"].append({
                    "endpoint": f"{method} {endpoint}",
                    "description": description,
                    "status_code": response.status_code,
                    "accessible": True
                })
                print(f"✅ {method} {endpoint} - {description} (Status: {response.status_code})")
                return {"implemented": True, "status": response.status_code}
            else:
                self.coverage_results["missing"].append({
                    "endpoint": f"{method} {endpoint}",
                    "description": description,
                    "status_code": 404
                })
                print(f"❌ {method} {endpoint} - {description} (NOT FOUND)")
                return {"implemented": False, "status": 404}
                
        except Exception as e:
            self.coverage_results["errors"].append({
                "endpoint": f"{method} {endpoint}",
                "description": description,
                "error": str(e)
            })
            print(f"🚨 {method} {endpoint} - {description} (ERROR: {e})")
            return {"implemented": False, "error": str(e)}
    
    def test_required_endpoints(self):
        """Test all required endpoints according to specification"""
        print("🔍 Testing Required Endpoint Coverage...")
        print("=" * 60)
        
        # Authentication Endpoints
        print("\n🔐 Authentication Endpoints:")
        self.test_endpoint("POST", "/api/auth/register", "User Registration")
        self.test_endpoint("POST", "/api/auth/login", "User Login")
        self.test_endpoint("POST", "/api/auth/logout", "User Logout")
        self.test_endpoint("GET", "/api/users/me", "Current User Profile")
        
        # Organization Endpoints
        print("\n🏢 Organization Endpoints:")
        self.test_endpoint("GET", "/api/organizations", "Get All Organizations")
        self.test_endpoint("POST", "/api/organizations", "Create Organization")
        self.test_endpoint("GET", "/api/organizations/{id}", "Get Organization by ID")
        self.test_endpoint("PUT", "/api/organizations/{id}", "Update Organization")
        self.test_endpoint("DELETE", "/api/organizations/{id}", "Delete Organization")
        
        # Project Endpoints
        print("\n📋 Project Endpoints:")
        self.test_endpoint("GET", "/api/projects", "Get All Projects")
        self.test_endpoint("POST", "/api/projects", "Create Project")
        self.test_endpoint("GET", "/api/projects/{id}", "Get Project by ID")
        self.test_endpoint("PUT", "/api/projects/{id}", "Update Project")
        self.test_endpoint("DELETE", "/api/projects/{id}", "Delete Project")
        
        # AI Projects Endpoints
        print("\n🤖 AI Projects Endpoints:")
        self.test_endpoint("GET", "/api/ai-projects", "Get AI Projects")
        self.test_endpoint("POST", "/api/ai-projects", "Create AI Project")
        self.test_endpoint("POST", "/api/projects/ai-generate", "Generate AI Project")
        
        # Kanban Board System
        print("\n📊 Kanban Board System:")
        self.test_endpoint("GET", "/api/boards", "Get All Boards")
        self.test_endpoint("POST", "/api/boards", "Create Board")
        self.test_endpoint("GET", "/api/projects/{id}/boards", "Get Project Boards")
        self.test_endpoint("GET", "/api/boards/{id}/columns", "Get Board Columns")
        self.test_endpoint("POST", "/api/boards/{id}/columns", "Create Column")
        
        # Task Management
        print("\n📝 Task Management:")
        self.test_endpoint("GET", "/api/columns/{id}/cards", "Get Column Cards")
        self.test_endpoint("POST", "/api/columns/{id}/cards", "Create Card")
        self.test_endpoint("GET", "/api/cards/{id}", "Get Card Details")
        self.test_endpoint("PUT", "/api/cards/{id}", "Update Card")
        self.test_endpoint("DELETE", "/api/cards/{id}", "Delete Card")
        self.test_endpoint("GET", "/api/cards/{id}/checklist", "Get Card Checklist")
        self.test_endpoint("POST", "/api/cards/{id}/checklist", "Create Checklist Item")
        self.test_endpoint("GET", "/api/cards/{id}/assignments", "Get Card Assignments")
        self.test_endpoint("POST", "/api/cards/{id}/assignments", "Assign User to Card")
        
        # Team Management
        print("\n👥 Team Management:")
        self.test_endpoint("GET", "/api/teams", "Get Teams")
        self.test_endpoint("POST", "/api/teams", "Create Team")
        self.test_endpoint("GET", "/api/teams/{id}/members", "Get Team Members")
        self.test_endpoint("POST", "/api/teams/{id}/members", "Add Team Member")
        
        # Monitoring Endpoints
        print("\n📊 Monitoring & Health:")
        self.test_endpoint("GET", "/health", "Basic Health Check")
        self.test_endpoint("GET", "/health/detailed", "Detailed Health Check")
        self.test_endpoint("GET", "/metrics", "Application Metrics")
        
        # User Management
        print("\n👤 User Management:")
        self.test_endpoint("GET", "/api/users", "Get Users")
        self.test_endpoint("GET", "/api/users/{id}", "Get User by ID")
        self.test_endpoint("PUT", "/api/users/{id}", "Update User")
        self.test_endpoint("DELETE", "/api/users/{id}", "Delete User")
        
        # File Upload
        print("\n📁 File Management:")
        self.test_endpoint("POST", "/api/upload", "File Upload")
        self.test_endpoint("GET", "/api/files/{id}", "Get File")
        self.test_endpoint("DELETE", "/api/files/{id}", "Delete File")
        
        # Notifications
        print("\n🔔 Notifications:")
        self.test_endpoint("GET", "/api/notifications", "Get Notifications")
        self.test_endpoint("PUT", "/api/notifications/{id}/read", "Mark Notification Read")
        self.test_endpoint("DELETE", "/api/notifications/{id}", "Delete Notification")
    
    def generate_report(self):
        """Generate comprehensive coverage report"""
        print("\n" + "=" * 60)
        print("📊 ENDPOINT COVERAGE REPORT")
        print("=" * 60)
        
        total_endpoints = len(self.coverage_results["implemented"]) + len(self.coverage_results["missing"])
        implemented_count = len(self.coverage_results["implemented"])
        missing_count = len(self.coverage_results["missing"])
        error_count = len(self.coverage_results["errors"])
        
        coverage_percentage = (implemented_count / total_endpoints * 100) if total_endpoints > 0 else 0
        
        print(f"📈 Coverage: {coverage_percentage:.1f}% ({implemented_count}/{total_endpoints})")
        print(f"✅ Implemented: {implemented_count}")
        print(f"❌ Missing: {missing_count}")
        print(f"🚨 Errors: {error_count}")
        
        if self.coverage_results["missing"]:
            print(f"\n❌ MISSING ENDPOINTS ({missing_count}):")
            for endpoint in self.coverage_results["missing"]:
                print(f"  - {endpoint['endpoint']}: {endpoint['description']}")
        
        if self.coverage_results["errors"]:
            print(f"\n🚨 ENDPOINT ERRORS ({error_count}):")
            for endpoint in self.coverage_results["errors"]:
                print(f"  - {endpoint['endpoint']}: {endpoint['error']}")
        
        print(f"\n✅ IMPLEMENTED ENDPOINTS ({implemented_count}):")
        for endpoint in self.coverage_results["implemented"]:
            print(f"  - {endpoint['endpoint']}: {endpoint['description']} (Status: {endpoint['status_code']})")
        
        return {
            "coverage_percentage": coverage_percentage,
            "implemented": implemented_count,
            "missing": missing_count,
            "errors": error_count,
            "details": self.coverage_results
        }
    
    def run_coverage_test(self):
        """Run complete endpoint coverage test"""
        print("🚀 Starting Endpoint Coverage Test...")
        print("=" * 60)
        
        # Authenticate first
        self.authenticate()
        
        # Test all endpoints
        self.test_required_endpoints()
        
        # Generate report
        return self.generate_report()

if __name__ == "__main__":
    tester = EndpointCoverageTest()
    results = tester.run_coverage_test()
