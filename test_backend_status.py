#!/usr/bin/env python3
"""
Quick test to check if backend is running and accessible
"""
import requests
import json
import sys

def test_backend_status():
    """Test if backend is running and accessible"""
    backend_url = "http://localhost:3001"
    
    print("🔍 Testing backend status...")
    
    try:
        # Test health endpoint
        response = requests.get(f"{backend_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running and accessible")
            print(f"📊 Health check response: {response.json()}")
            return True
        else:
            print(f"❌ Backend responded with status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Backend is not running or not accessible")
        print("🔧 Please start the backend server first")
        return False
    except Exception as e:
        print(f"❌ Error testing backend: {e}")
        return False

def test_api_docs():
    """Test if API documentation is accessible"""
    backend_url = "http://localhost:3001"
    
    try:
        response = requests.get(f"{backend_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API documentation is accessible")
            return True
        else:
            print(f"❌ API docs responded with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error accessing API docs: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Backend Status Check")
    print("=" * 50)
    
    backend_running = test_backend_status()
    
    if backend_running:
        test_api_docs()
        print("\n✅ Backend is ready for testing!")
        sys.exit(0)
    else:
        print("\n❌ Backend is not ready. Please start the backend server.")
        sys.exit(1)
