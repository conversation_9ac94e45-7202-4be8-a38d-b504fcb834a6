// src/utils/organizationService.js

// Placeholder data structure for demo/testing
const mockOrganizations = [
  {
    id: 'org-1',
    name: 'Demo Organization',
    domain: 'demo.com',
    createdAt: new Date().toISOString(),
    description: 'A demo organization for testing',
    logo_url: null
  },
  {
    id: 'org-2',
    name: 'Acme Corp',
    domain: 'acme.com',
    createdAt: new Date().toISOString(),
    description: 'Acme Corporation',
    logo_url: null
  },
];

// Mock integrations data
const mockIntegrations = [
  {
    id: 'int-1',
    name: 'Slack',
    type: 'communication',
    status: 'connected',
    description: 'Team communication platform',
    icon: 'MessageSquare',
    connected_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    last_sync: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
  },
  {
    id: 'int-2',
    name: 'GitH<PERSON>',
    type: 'development',
    status: 'connected',
    description: 'Code repository and version control',
    icon: 'Github',
    connected_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    last_sync: new Date(Date.now() - 1800000).toISOString() // 30 minutes ago
  },
  {
    id: 'int-3',
    name: 'Jira',
    type: 'project_management',
    status: 'disconnected',
    description: 'Issue tracking and project management',
    icon: 'Kanban',
    connected_at: null,
    last_sync: null
  }
];

// Mock activities data
const mockActivities = [
  {
    id: 'act-1',
    type: 'integration_connected',
    title: 'Slack integration connected',
    description: 'Successfully connected Slack workspace',
    user: 'John Doe',
    occurred_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
    icon: 'MessageSquare'
  },
  {
    id: 'act-2',
    type: 'user_invited',
    title: 'New team member invited',
    description: 'Invited <EMAIL> to join the organization',
    user: 'Admin User',
    occurred_at: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
    icon: 'UserPlus'
  },
  {
    id: 'act-3',
    type: 'project_created',
    title: 'New project created',
    description: 'Created "Website Redesign" project',
    user: 'John Doe',
    occurred_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    icon: 'FolderPlus'
  }
];

// Mock notifications data
const mockNotifications = [
  {
    id: 'notif-1',
    title: 'Integration sync completed',
    message: 'GitHub integration has successfully synced 15 repositories',
    type: 'success',
    read_at: null,
    created_at: new Date(Date.now() - 1800000).toISOString() // 30 minutes ago
  },
  {
    id: 'notif-2',
    title: 'New team member joined',
    message: 'Jane Smith has accepted the invitation and joined your organization',
    type: 'info',
    read_at: null,
    created_at: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
  },
  {
    id: 'notif-3',
    title: 'Monthly usage report',
    message: 'Your organization usage report for this month is ready',
    type: 'info',
    read_at: new Date(Date.now() - 1800000).toISOString(),
    created_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
  }
];

// Simulate API delay
const delay = (ms) => new Promise((res) => setTimeout(res, ms));

export const getOrganizations = async () => {
  await delay(500);
  return { data: mockOrganizations, error: null };
};

export const getOrganizationById = async (id) => {
  await delay(500);
  const org = mockOrganizations.find((org) => org.id === id);
  return { data: org || null, error: org ? null : 'Organization not found' };
};

export const createOrganization = async (orgData, logoFile = null) => {
  try {
    await delay(800); // Simulate API call delay

    // Validate required fields
    if (!orgData.name || !orgData.name.trim()) {
      return { data: null, error: 'Organization name is required' };
    }

    // Validate email format if provided
    if (orgData.contact_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(orgData.contact_email)) {
      return { data: null, error: 'Invalid contact email format' };
    }

    // Handle logo upload simulation
    let logoUrl = null;
    if (logoFile) {
      // Simulate file upload delay
      await delay(300);

      // Validate file type
      if (!logoFile.type.startsWith('image/')) {
        return { data: null, error: 'Logo must be an image file' };
      }

      // Validate file size (max 5MB)
      if (logoFile.size > 5 * 1024 * 1024) {
        return { data: null, error: 'Logo file size must be less than 5MB' };
      }

      // Simulate uploaded logo URL
      logoUrl = `/uploads/organizations/org-${mockOrganizations.length + 1}/logo/${logoFile.name}`;
    }

    // Create new organization with all fields
    const newOrg = {
      id: `org-${mockOrganizations.length + 1}`,
      name: orgData.name.trim(),
      description: orgData.description?.trim() || null,
      website: orgData.website?.trim() || null,
      industry: orgData.industry || null,
      size: orgData.size || null,
      organization_category: orgData.organization_category || null,

      // Contact information
      contact_email: orgData.contact_email?.trim() || null,
      contact_phone: orgData.contact_phone?.trim() || null,

      // Address details
      address_line1: orgData.address_line1?.trim() || null,
      address_line2: orgData.address_line2?.trim() || null,
      city: orgData.city?.trim() || null,
      state: orgData.state?.trim() || null,
      postal_code: orgData.postal_code?.trim() || null,
      country: orgData.country || null,

      // Regional settings
      timezone: orgData.timezone || 'UTC',
      language: orgData.language || 'en',
      allowed_domains: orgData.allowed_domains || null,

      // System fields
      logo_url: logoUrl,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      created_by: 'current-user-id', // In real app, this would come from auth context

      // Mock additional fields
      member_count: 1, // Creator is the first member
      project_count: 0,
      status: 'active'
    };

    // Add to mock data
    mockOrganizations.push(newOrg);

    return {
      data: newOrg,
      error: null,
      message: `Organization "${newOrg.name}" created successfully!`
    };
  } catch (error) {
    console.error('Error creating organization:', error);
    return {
      data: null,
      error: error.message || 'Failed to create organization. Please try again.'
    };
  }
};

// Get organization dashboard data
export const getOrganizationDashboard = async (organizationId) => {
  await delay(300);
  const org = mockOrganizations.find(org => org.id === organizationId);
  if (!org) {
    return { success: false, error: 'Organization not found' };
  }
  return {
    success: true,
    data: {
      ...org,
      member_count: 12,
      project_count: 8,
      integration_count: mockIntegrations.filter(i => i.status === 'connected').length
    }
  };
};

// Get organization integrations
export const getIntegrations = async (organizationId) => {
  await delay(300);
  return {
    success: true,
    data: mockIntegrations
  };
};

// Get recent activities
export const getRecentActivities = async (organizationId) => {
  await delay(300);
  return {
    success: true,
    data: mockActivities
  };
};

// Get notifications
export const getNotifications = async (organizationId) => {
  await delay(300);
  return {
    success: true,
    data: mockNotifications
  };
};

// Mark notification as read
export const markNotificationAsRead = async (notificationId) => {
  await delay(200);
  const notification = mockNotifications.find(n => n.id === notificationId);
  if (notification) {
    notification.read_at = new Date().toISOString();
    return { success: true };
  }
  return { success: false, error: 'Notification not found' };
};

// Update integration
export const updateIntegration = async (integrationId, updateData) => {
  await delay(300);
  const integration = mockIntegrations.find(i => i.id === integrationId);
  if (integration) {
    Object.assign(integration, updateData);
    return { success: true, data: integration };
  }
  return { success: false, error: 'Integration not found' };
};

// Update organization
export const updateOrganization = async (organizationId, orgData, logoFile = null) => {
  try {
    await delay(600);

    const orgIndex = mockOrganizations.findIndex(org => org.id === organizationId);
    if (orgIndex === -1) {
      return { data: null, error: 'Organization not found' };
    }

    // Validate email format if provided
    if (orgData.contact_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(orgData.contact_email)) {
      return { data: null, error: 'Invalid contact email format' };
    }

    // Handle logo upload if provided
    let logoUrl = mockOrganizations[orgIndex].logo_url;
    if (logoFile) {
      await delay(300);

      if (!logoFile.type.startsWith('image/')) {
        return { data: null, error: 'Logo must be an image file' };
      }

      if (logoFile.size > 5 * 1024 * 1024) {
        return { data: null, error: 'Logo file size must be less than 5MB' };
      }

      logoUrl = `/uploads/organizations/${organizationId}/logo/${logoFile.name}`;
    }

    // Update organization
    const updatedOrg = {
      ...mockOrganizations[orgIndex],
      ...orgData,
      logo_url: logoUrl,
      updated_at: new Date().toISOString()
    };

    mockOrganizations[orgIndex] = updatedOrg;

    return {
      data: updatedOrg,
      error: null,
      message: 'Organization updated successfully!'
    };
  } catch (error) {
    console.error('Error updating organization:', error);
    return {
      data: null,
      error: error.message || 'Failed to update organization. Please try again.'
    };
  }
};

// Upload organization logo
export const uploadOrganizationLogo = async (organizationId, logoFile) => {
  try {
    await delay(500);

    if (!logoFile) {
      return { data: null, error: 'No file provided' };
    }

    if (!logoFile.type.startsWith('image/')) {
      return { data: null, error: 'File must be an image' };
    }

    if (logoFile.size > 5 * 1024 * 1024) {
      return { data: null, error: 'File size must be less than 5MB' };
    }

    const logoUrl = `/uploads/organizations/${organizationId}/logo/${logoFile.name}`;

    // Update organization logo URL
    const orgIndex = mockOrganizations.findIndex(org => org.id === organizationId);
    if (orgIndex !== -1) {
      mockOrganizations[orgIndex].logo_url = logoUrl;
      mockOrganizations[orgIndex].updated_at = new Date().toISOString();
    }

    return {
      data: { logo_url: logoUrl },
      error: null,
      message: 'Logo uploaded successfully!'
    };
  } catch (error) {
    console.error('Error uploading logo:', error);
    return {
      data: null,
      error: error.message || 'Failed to upload logo. Please try again.'
    };
  }
};

// Delete organization logo
export const deleteOrganizationLogo = async (organizationId) => {
  try {
    await delay(300);

    const orgIndex = mockOrganizations.findIndex(org => org.id === organizationId);
    if (orgIndex === -1) {
      return { data: null, error: 'Organization not found' };
    }

    if (!mockOrganizations[orgIndex].logo_url) {
      return { data: null, error: 'No logo to delete' };
    }

    mockOrganizations[orgIndex].logo_url = null;
    mockOrganizations[orgIndex].updated_at = new Date().toISOString();

    return {
      data: { success: true },
      error: null,
      message: 'Logo deleted successfully!'
    };
  } catch (error) {
    console.error('Error deleting logo:', error);
    return {
      data: null,
      error: error.message || 'Failed to delete logo. Please try again.'
    };
  }
};

// Default export for backward compatibility
const organizationService = {
  getOrganizations,
  getOrganizationById,
  createOrganization,
  updateOrganization,
  uploadOrganizationLogo,
  deleteOrganizationLogo,
  getOrganizationDashboard,
  getIntegrations,
  getRecentActivities,
  getNotifications,
  markNotificationAsRead,
  updateIntegration
};

export default organizationService;
