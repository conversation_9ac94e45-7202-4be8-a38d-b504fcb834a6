#!/usr/bin/env python3
"""
Production Readiness Assessment for Agno WorkSphere
Tests connection pooling, concurrent users, monitoring, and error handling
"""
import asyncio
import aiohttp
import time
import json
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any

BASE_URL = "http://localhost:3001"

class ProductionReadinessTest:
    def __init__(self):
        self.test_results = {
            "passed": 0,
            "failed": 0,
            "errors": [],
            "performance_metrics": {}
        }
    
    def log_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        if success:
            self.test_results["passed"] += 1
            print(f"✅ {test_name}: PASSED {details}")
        else:
            self.test_results["failed"] += 1
            self.test_results["errors"].append(f"{test_name}: {details}")
            print(f"❌ {test_name}: FAILED {details}")
    
    def test_health_monitoring(self):
        """Test health monitoring endpoints"""
        print("\n🏥 Testing Health Monitoring...")
        
        import requests
        
        # Test basic health check
        try:
            start_time = time.time()
            response = requests.get(f"{BASE_URL}/health", timeout=5)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                if "status" in data and data["status"] == "healthy":
                    self.log_result("Basic Health Check", True, f"Response time: {response_time:.3f}s")
                    self.test_results["performance_metrics"]["health_response_time"] = response_time
                else:
                    self.log_result("Basic Health Check", False, "Invalid health status")
            else:
                self.log_result("Basic Health Check", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Basic Health Check", False, f"Error: {e}")
        
        # Test detailed health check
        try:
            response = requests.get(f"{BASE_URL}/health/detailed", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                required_fields = ["database", "version", "user_count"]
                missing_fields = [field for field in required_fields if field not in data]
                
                if not missing_fields:
                    self.log_result("Detailed Health Check", True, "All required fields present")
                else:
                    self.log_result("Detailed Health Check", False, f"Missing fields: {missing_fields}")
            else:
                self.log_result("Detailed Health Check", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Detailed Health Check", False, f"Error: {e}")
        
        # Test metrics endpoint
        try:
            response = requests.get(f"{BASE_URL}/metrics", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if "database" in data and "application" in data:
                    self.log_result("Metrics Endpoint", True, "Metrics data available")
                else:
                    self.log_result("Metrics Endpoint", False, "Incomplete metrics data")
            else:
                self.log_result("Metrics Endpoint", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Metrics Endpoint", False, f"Error: {e}")
    
    def test_concurrent_users(self, num_users: int = 10):
        """Test concurrent user scenarios"""
        print(f"\n👥 Testing Concurrent Users ({num_users} users)...")
        
        import requests
        
        def make_request(user_id: int) -> Dict[str, Any]:
            """Make a request as a specific user"""
            try:
                start_time = time.time()
                
                # Test health endpoint (no auth required)
                response = requests.get(f"{BASE_URL}/health", timeout=10)
                
                end_time = time.time()
                response_time = end_time - start_time
                
                return {
                    "user_id": user_id,
                    "success": response.status_code == 200,
                    "status_code": response.status_code,
                    "response_time": response_time,
                    "error": None
                }
                
            except Exception as e:
                return {
                    "user_id": user_id,
                    "success": False,
                    "status_code": None,
                    "response_time": None,
                    "error": str(e)
                }
        
        # Execute concurrent requests
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_users) as executor:
            futures = [executor.submit(make_request, i) for i in range(num_users)]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze results
        successful_requests = [r for r in results if r["success"]]
        failed_requests = [r for r in results if not r["success"]]
        
        success_rate = len(successful_requests) / len(results) * 100
        avg_response_time = sum(r["response_time"] for r in successful_requests if r["response_time"]) / len(successful_requests) if successful_requests else 0
        
        self.test_results["performance_metrics"]["concurrent_users"] = {
            "total_requests": len(results),
            "successful_requests": len(successful_requests),
            "failed_requests": len(failed_requests),
            "success_rate": success_rate,
            "avg_response_time": avg_response_time,
            "total_time": total_time
        }
        
        if success_rate >= 95:
            self.log_result("Concurrent Users Test", True, f"{success_rate:.1f}% success rate, avg {avg_response_time:.3f}s")
        else:
            self.log_result("Concurrent Users Test", False, f"{success_rate:.1f}% success rate (below 95%)")
    
    def test_connection_pooling(self):
        """Test database connection pooling under load"""
        print("\n🔗 Testing Connection Pooling...")
        
        import requests
        
        def make_db_request() -> Dict[str, Any]:
            """Make a request that requires database access"""
            try:
                start_time = time.time()
                
                # Test organizations endpoint (requires DB)
                response = requests.get(f"{BASE_URL}/api/organizations", timeout=10)
                
                end_time = time.time()
                response_time = end_time - start_time
                
                return {
                    "success": response.status_code == 200,
                    "response_time": response_time,
                    "error": None
                }
                
            except Exception as e:
                return {
                    "success": False,
                    "response_time": None,
                    "error": str(e)
                }
        
        # Test with multiple concurrent DB requests
        num_requests = 20
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_db_request) for _ in range(num_requests)]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        successful_requests = [r for r in results if r["success"]]
        success_rate = len(successful_requests) / len(results) * 100
        avg_response_time = sum(r["response_time"] for r in successful_requests if r["response_time"]) / len(successful_requests) if successful_requests else 0
        
        self.test_results["performance_metrics"]["connection_pooling"] = {
            "total_requests": len(results),
            "successful_requests": len(successful_requests),
            "success_rate": success_rate,
            "avg_response_time": avg_response_time,
            "total_time": total_time
        }
        
        if success_rate >= 90 and avg_response_time < 2.0:
            self.log_result("Connection Pooling", True, f"{success_rate:.1f}% success, {avg_response_time:.3f}s avg")
        else:
            self.log_result("Connection Pooling", False, f"{success_rate:.1f}% success, {avg_response_time:.3f}s avg")
    
    def test_error_handling(self):
        """Test graceful error handling"""
        print("\n🚨 Testing Error Handling...")
        
        import requests
        
        # Test invalid endpoint
        try:
            response = requests.get(f"{BASE_URL}/api/invalid-endpoint", timeout=5)
            
            if response.status_code == 404:
                self.log_result("Invalid Endpoint Handling", True, "Returns 404 as expected")
            else:
                self.log_result("Invalid Endpoint Handling", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Invalid Endpoint Handling", False, f"Error: {e}")
        
        # Test malformed request
        try:
            response = requests.post(
                f"{BASE_URL}/api/auth/login",
                json={"invalid": "data"},
                timeout=5
            )
            
            if response.status_code in [400, 422]:
                self.log_result("Malformed Request Handling", True, f"Returns {response.status_code} as expected")
            else:
                self.log_result("Malformed Request Handling", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Malformed Request Handling", False, f"Error: {e}")
        
        # Test server error handling (if possible)
        try:
            # Try to access an endpoint that might cause server error
            response = requests.get(f"{BASE_URL}/api/projects/invalid-uuid", timeout=5)
            
            if response.status_code in [400, 404, 500]:
                self.log_result("Server Error Handling", True, f"Handles errors gracefully ({response.status_code})")
            else:
                self.log_result("Server Error Handling", False, f"Unexpected status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Server Error Handling", False, f"Error: {e}")
    
    def test_response_times(self):
        """Test API response times under normal load"""
        print("\n⚡ Testing Response Times...")
        
        import requests
        
        endpoints_to_test = [
            ("/health", "Health Check"),
            ("/api/organizations", "Organizations"),
            ("/api/projects", "Projects"),
            ("/api/boards", "Boards"),
            ("/metrics", "Metrics")
        ]
        
        response_times = {}
        
        for endpoint, name in endpoints_to_test:
            try:
                start_time = time.time()
                response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
                end_time = time.time()
                
                response_time = end_time - start_time
                response_times[name] = response_time
                
                if response_time < 1.0:  # Less than 1 second
                    self.log_result(f"{name} Response Time", True, f"{response_time:.3f}s")
                else:
                    self.log_result(f"{name} Response Time", False, f"{response_time:.3f}s (too slow)")
                    
            except Exception as e:
                self.log_result(f"{name} Response Time", False, f"Error: {e}")
        
        self.test_results["performance_metrics"]["response_times"] = response_times
    
    def generate_production_report(self):
        """Generate comprehensive production readiness report"""
        print("\n" + "=" * 60)
        print("🏭 PRODUCTION READINESS REPORT")
        print("=" * 60)
        
        print(f"✅ Passed Tests: {self.test_results['passed']}")
        print(f"❌ Failed Tests: {self.test_results['failed']}")
        
        if self.test_results["errors"]:
            print(f"\n🚨 FAILED TESTS:")
            for error in self.test_results["errors"]:
                print(f"  - {error}")
        
        # Performance metrics summary
        if self.test_results["performance_metrics"]:
            print(f"\n📊 PERFORMANCE METRICS:")
            
            metrics = self.test_results["performance_metrics"]
            
            if "health_response_time" in metrics:
                print(f"  🏥 Health Check: {metrics['health_response_time']:.3f}s")
            
            if "concurrent_users" in metrics:
                cu = metrics["concurrent_users"]
                print(f"  👥 Concurrent Users: {cu['success_rate']:.1f}% success, {cu['avg_response_time']:.3f}s avg")
            
            if "connection_pooling" in metrics:
                cp = metrics["connection_pooling"]
                print(f"  🔗 Connection Pooling: {cp['success_rate']:.1f}% success, {cp['avg_response_time']:.3f}s avg")
            
            if "response_times" in metrics:
                print(f"  ⚡ Response Times:")
                for endpoint, time_val in metrics["response_times"].items():
                    print(f"    - {endpoint}: {time_val:.3f}s")
        
        # Calculate readiness score
        total_tests = self.test_results["passed"] + self.test_results["failed"]
        readiness_score = (self.test_results["passed"] / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n🎯 Production Readiness Score: {readiness_score:.1f}%")
        
        if readiness_score >= 90:
            print("🟢 READY FOR PRODUCTION")
        elif readiness_score >= 75:
            print("🟡 MOSTLY READY - Minor issues to address")
        else:
            print("🔴 NOT READY - Significant issues need fixing")
        
        return self.test_results
    
    def run_all_tests(self):
        """Run all production readiness tests"""
        print("🚀 Starting Production Readiness Assessment...")
        print("=" * 60)
        
        start_time = time.time()
        
        self.test_health_monitoring()
        self.test_response_times()
        self.test_concurrent_users(10)
        self.test_connection_pooling()
        self.test_error_handling()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n⏱️ Total Test Duration: {duration:.2f} seconds")
        
        return self.generate_production_report()

if __name__ == "__main__":
    tester = ProductionReadinessTest()
    results = tester.run_all_tests()
