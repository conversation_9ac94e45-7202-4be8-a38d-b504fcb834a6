#!/usr/bin/env python3
"""
Database Optimization and Indexing Script
Comprehensive database performance optimization with indexing, query optimization, and maintenance
"""
import asyncio
import asyncpg
import time
from typing import List, Dict, Any
import structlog

logger = structlog.get_logger()

DATABASE_URL = "postgresql://postgres:admin@localhost:5432/agno_worksphere"

class DatabaseOptimizer:
    """Database optimization and maintenance manager"""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.optimization_results = []
    
    async def connect(self):
        """Create database connection"""
        return await asyncpg.connect(self.database_url)
    
    async def create_indexes(self, conn: asyncpg.Connection):
        """Create optimized indexes for better performance"""
        logger.info("Creating database indexes for performance optimization")
        
        indexes = [
            # User-related indexes
            {
                "name": "idx_users_email_unique",
                "sql": "CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_unique ON users(email)",
                "description": "Unique index on user email for fast lookups and constraint enforcement"
            },
            {
                "name": "idx_users_email_verified",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_verified ON users(email_verified) WHERE email_verified = true",
                "description": "Partial index for verified users"
            },
            {
                "name": "idx_users_created_at",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users(created_at DESC)",
                "description": "Index for user creation date queries"
            },
            
            # Organization-related indexes
            {
                "name": "idx_organizations_name",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_organizations_name ON organizations(name)",
                "description": "Index for organization name searches"
            },
            {
                "name": "idx_organization_members_user_org",
                "sql": "CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_organization_members_user_org ON organization_members(user_id, organization_id)",
                "description": "Composite unique index for user-organization relationships"
            },
            {
                "name": "idx_organization_members_org_role",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_organization_members_org_role ON organization_members(organization_id, role)",
                "description": "Index for finding users by organization and role"
            },
            
            # Project-related indexes
            {
                "name": "idx_projects_organization",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_organization ON projects(organization_id)",
                "description": "Index for projects by organization"
            },
            {
                "name": "idx_projects_status_priority",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_status_priority ON projects(status, priority)",
                "description": "Composite index for project filtering by status and priority"
            },
            {
                "name": "idx_projects_created_by",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_created_by ON projects(created_by)",
                "description": "Index for projects by creator"
            },
            {
                "name": "idx_projects_dates",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_projects_dates ON projects(start_date, end_date)",
                "description": "Index for project date range queries"
            },
            
            # Board and column indexes
            {
                "name": "idx_boards_project",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_boards_project ON boards(project_id)",
                "description": "Index for boards by project"
            },
            {
                "name": "idx_columns_board_position",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_columns_board_position ON columns(board_id, position)",
                "description": "Index for columns by board and position"
            },
            
            # Card (task) related indexes
            {
                "name": "idx_cards_column_position",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_column_position ON cards(column_id, position)",
                "description": "Index for cards by column and position"
            },
            {
                "name": "idx_cards_created_by",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_created_by ON cards(created_by)",
                "description": "Index for cards by creator"
            },
            {
                "name": "idx_cards_priority_due_date",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_priority_due_date ON cards(priority, due_date)",
                "description": "Index for card filtering by priority and due date"
            },
            {
                "name": "idx_cards_title_search",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cards_title_search ON cards USING gin(to_tsvector('english', title))",
                "description": "Full-text search index for card titles"
            },
            
            # Checklist items indexes
            {
                "name": "idx_checklist_items_card_position",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_checklist_items_card_position ON checklist_items(card_id, position)",
                "description": "Index for checklist items by card and position"
            },
            {
                "name": "idx_checklist_items_completed",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_checklist_items_completed ON checklist_items(card_id, completed)",
                "description": "Index for checklist completion status"
            },
            
            # Card assignments indexes
            {
                "name": "idx_card_assignments_card_user",
                "sql": "CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_card_assignments_card_user ON card_assignments(card_id, user_id)",
                "description": "Unique index for card-user assignments"
            },
            {
                "name": "idx_card_assignments_user",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_card_assignments_user ON card_assignments(user_id)",
                "description": "Index for assignments by user"
            },
            
            # Session management indexes
            {
                "name": "idx_sessions_user_active",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_user_active ON sessions(user_id, is_active) WHERE is_active = true",
                "description": "Partial index for active user sessions"
            },
            {
                "name": "idx_sessions_expires_at",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at) WHERE expires_at > NOW()",
                "description": "Partial index for non-expired sessions"
            },
            
            # Attachment indexes
            {
                "name": "idx_attachments_card",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attachments_card ON attachments(card_id)",
                "description": "Index for attachments by card"
            },
            {
                "name": "idx_attachments_uploaded_by",
                "sql": "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attachments_uploaded_by ON attachments(uploaded_by)",
                "description": "Index for attachments by uploader"
            },
            
            # User preferences indexes
            {
                "name": "idx_user_preferences_user",
                "sql": "CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_user_preferences_user ON user_preferences(user_id)",
                "description": "Unique index for user preferences"
            }
        ]
        
        created_count = 0
        failed_count = 0
        
        for index in indexes:
            try:
                start_time = time.time()
                await conn.execute(index["sql"])
                creation_time = time.time() - start_time
                
                logger.info(
                    "Index created successfully",
                    name=index["name"],
                    creation_time=f"{creation_time:.2f}s",
                    description=index["description"]
                )
                created_count += 1
                
            except asyncpg.exceptions.DuplicateTableError:
                logger.debug("Index already exists", name=index["name"])
                created_count += 1
                
            except Exception as e:
                logger.error(
                    "Failed to create index",
                    name=index["name"],
                    error=str(e)
                )
                failed_count += 1
        
        self.optimization_results.append({
            "operation": "create_indexes",
            "created": created_count,
            "failed": failed_count,
            "total": len(indexes)
        })
        
        logger.info(
            "Index creation completed",
            created=created_count,
            failed=failed_count,
            total=len(indexes)
        )
    
    async def analyze_tables(self, conn: asyncpg.Connection):
        """Analyze tables to update statistics for query planner"""
        logger.info("Analyzing tables to update query planner statistics")
        
        tables = [
            "users", "organizations", "organization_members", "projects",
            "boards", "columns", "cards", "checklist_items", "card_assignments",
            "sessions", "attachments", "user_preferences", "user_sessions"
        ]
        
        analyzed_count = 0
        
        for table in tables:
            try:
                start_time = time.time()
                await conn.execute(f"ANALYZE {table}")
                analyze_time = time.time() - start_time
                
                logger.debug(
                    "Table analyzed",
                    table=table,
                    analyze_time=f"{analyze_time:.2f}s"
                )
                analyzed_count += 1
                
            except Exception as e:
                logger.error("Failed to analyze table", table=table, error=str(e))
        
        self.optimization_results.append({
            "operation": "analyze_tables",
            "analyzed": analyzed_count,
            "total": len(tables)
        })
        
        logger.info("Table analysis completed", analyzed=analyzed_count, total=len(tables))
    
    async def optimize_database_settings(self, conn: asyncpg.Connection):
        """Optimize database configuration settings"""
        logger.info("Optimizing database configuration settings")
        
        # Get current settings
        current_settings = {}
        settings_query = """
            SELECT name, setting, unit, context 
            FROM pg_settings 
            WHERE name IN (
                'shared_buffers', 'effective_cache_size', 'maintenance_work_mem',
                'checkpoint_completion_target', 'wal_buffers', 'default_statistics_target',
                'random_page_cost', 'effective_io_concurrency'
            )
        """
        
        settings_rows = await conn.fetch(settings_query)
        for row in settings_rows:
            current_settings[row['name']] = {
                'value': row['setting'],
                'unit': row['unit'],
                'context': row['context']
            }
        
        logger.info("Current database settings", settings=current_settings)
        
        # Note: Most PostgreSQL settings require restart and superuser privileges
        # We'll log recommendations instead of trying to change them
        recommendations = {
            "shared_buffers": "25% of total RAM (requires restart)",
            "effective_cache_size": "75% of total RAM",
            "maintenance_work_mem": "256MB - 1GB depending on RAM",
            "checkpoint_completion_target": "0.9",
            "wal_buffers": "16MB",
            "default_statistics_target": "100",
            "random_page_cost": "1.1 for SSD, 4.0 for HDD",
            "effective_io_concurrency": "200 for SSD, 2 for HDD"
        }
        
        logger.info("Database optimization recommendations", recommendations=recommendations)
        
        self.optimization_results.append({
            "operation": "database_settings",
            "current_settings": current_settings,
            "recommendations": recommendations
        })
    
    async def vacuum_and_reindex(self, conn: asyncpg.Connection):
        """Perform vacuum and reindex operations"""
        logger.info("Performing vacuum and reindex operations")
        
        # Get table sizes before optimization
        table_sizes_before = await conn.fetch("""
            SELECT 
                schemaname,
                tablename,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
            FROM pg_tables 
            WHERE schemaname = 'public'
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        """)
        
        # Vacuum analyze all tables
        try:
            await conn.execute("VACUUM ANALYZE")
            logger.info("VACUUM ANALYZE completed successfully")
        except Exception as e:
            logger.error("VACUUM ANALYZE failed", error=str(e))
        
        # Get table sizes after optimization
        table_sizes_after = await conn.fetch("""
            SELECT 
                schemaname,
                tablename,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
            FROM pg_tables 
            WHERE schemaname = 'public'
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        """)
        
        self.optimization_results.append({
            "operation": "vacuum_reindex",
            "table_sizes_before": [dict(row) for row in table_sizes_before],
            "table_sizes_after": [dict(row) for row in table_sizes_after]
        })
        
        logger.info("Vacuum and reindex operations completed")
    
    async def check_query_performance(self, conn: asyncpg.Connection):
        """Check for slow queries and performance issues"""
        logger.info("Checking query performance and identifying slow queries")
        
        # Enable query statistics if not already enabled
        try:
            await conn.execute("CREATE EXTENSION IF NOT EXISTS pg_stat_statements")
        except Exception as e:
            logger.warning("Could not enable pg_stat_statements", error=str(e))
        
        # Check for missing indexes
        missing_indexes_query = """
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats
            WHERE schemaname = 'public'
            AND n_distinct > 100
            AND correlation < 0.1
        """
        
        try:
            missing_indexes = await conn.fetch(missing_indexes_query)
            logger.info("Potential missing indexes identified", count=len(missing_indexes))
            
            for row in missing_indexes:
                logger.info(
                    "Consider adding index",
                    table=row['tablename'],
                    column=row['attname'],
                    n_distinct=row['n_distinct']
                )
        except Exception as e:
            logger.error("Failed to check for missing indexes", error=str(e))
        
        self.optimization_results.append({
            "operation": "query_performance_check",
            "potential_missing_indexes": len(missing_indexes) if 'missing_indexes' in locals() else 0
        })
    
    async def run_full_optimization(self):
        """Run complete database optimization"""
        logger.info("Starting comprehensive database optimization")
        start_time = time.time()
        
        conn = await self.connect()
        
        try:
            # Run all optimization steps
            await self.create_indexes(conn)
            await self.analyze_tables(conn)
            await self.optimize_database_settings(conn)
            await self.vacuum_and_reindex(conn)
            await self.check_query_performance(conn)
            
            total_time = time.time() - start_time
            
            logger.info(
                "Database optimization completed successfully",
                total_time=f"{total_time:.2f}s",
                results=self.optimization_results
            )
            
            return {
                "success": True,
                "total_time": total_time,
                "results": self.optimization_results
            }
            
        except Exception as e:
            logger.error("Database optimization failed", error=str(e))
            return {
                "success": False,
                "error": str(e),
                "partial_results": self.optimization_results
            }
        
        finally:
            await conn.close()

async def main():
    """Main optimization function"""
    optimizer = DatabaseOptimizer(DATABASE_URL)
    result = await optimizer.run_full_optimization()
    
    if result["success"]:
        print("✅ Database optimization completed successfully!")
        print(f"⏱️ Total time: {result['total_time']:.2f} seconds")
        
        for operation_result in result["results"]:
            operation = operation_result["operation"]
            print(f"\n📊 {operation.replace('_', ' ').title()}:")
            
            if operation == "create_indexes":
                print(f"  • Created: {operation_result['created']}/{operation_result['total']} indexes")
                if operation_result['failed'] > 0:
                    print(f"  • Failed: {operation_result['failed']} indexes")
            
            elif operation == "analyze_tables":
                print(f"  • Analyzed: {operation_result['analyzed']}/{operation_result['total']} tables")
            
            elif operation == "vacuum_reindex":
                print(f"  • VACUUM ANALYZE completed")
                print(f"  • Table count: {len(operation_result['table_sizes_after'])}")
            
            elif operation == "query_performance_check":
                print(f"  • Potential missing indexes: {operation_result['potential_missing_indexes']}")
    
    else:
        print("❌ Database optimization failed!")
        print(f"Error: {result['error']}")

if __name__ == "__main__":
    asyncio.run(main())
