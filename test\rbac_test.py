#!/usr/bin/env python3
"""
Role-Based Access Control (RBAC) Test for Agno WorkSphere
Tests authentication, authorization, and role-specific access
"""
import requests
import json
import time
from typing import Dict, List, Any, Optional

BASE_URL = "http://localhost:3001"

class RBACTester:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = {
            "passed": 0,
            "failed": 0,
            "errors": []
        }
        self.test_users = {
            "owner": {"email": "<EMAIL>", "password": "owner123", "token": None},
            "admin": {"email": "<EMAIL>", "password": "admin123", "token": None},
            "member": {"email": "<EMAIL>", "password": "member123", "token": None},
            "viewer": {"email": "<EMAIL>", "password": "viewer123", "token": None}
        }
    
    def log_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        if success:
            self.test_results["passed"] += 1
            print(f"✅ {test_name}: PASSED {details}")
        else:
            self.test_results["failed"] += 1
            self.test_results["errors"].append(f"{test_name}: {details}")
            print(f"❌ {test_name}: FAILED {details}")
    
    def test_authentication_flow(self):
        """Test basic authentication functionality"""
        print("\n🔐 Testing Authentication Flow...")
        
        # Test user registration
        registration_data = {
            "email": "<EMAIL>",
            "password": "testpass123",
            "first_name": "Test",
            "last_name": "User",
            "organization_name": "Test Org"
        }
        
        try:
            response = self.session.post(
                f"{BASE_URL}/api/auth/register",
                json=registration_data,
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                self.log_result("User Registration", True, f"Status: {response.status_code}")
            else:
                self.log_result("User Registration", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("User Registration", False, f"Error: {e}")
        
        # Test user login
        login_data = {
            "email": "<EMAIL>",
            "password": "testpass123"
        }
        
        try:
            response = self.session.post(
                f"{BASE_URL}/api/auth/login",
                json=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if "access_token" in data:
                    self.log_result("User Login", True, "Token received")
                    return data["access_token"]
                else:
                    self.log_result("User Login", False, "No token in response")
            else:
                self.log_result("User Login", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("User Login", False, f"Error: {e}")
        
        return None
    
    def test_jwt_token_validation(self, token: str):
        """Test JWT token validation"""
        print("\n🔑 Testing JWT Token Validation...")
        
        if not token:
            self.log_result("JWT Token Validation", False, "No token available")
            return
        
        # Test valid token
        try:
            response = self.session.get(
                f"{BASE_URL}/api/users/me",
                headers={"Authorization": f"Bearer {token}"},
                timeout=10
            )
            
            if response.status_code == 200:
                self.log_result("Valid Token Access", True, "User profile retrieved")
            else:
                self.log_result("Valid Token Access", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Valid Token Access", False, f"Error: {e}")
        
        # Test invalid token
        try:
            response = self.session.get(
                f"{BASE_URL}/api/users/me",
                headers={"Authorization": "Bearer invalid_token_123"},
                timeout=10
            )
            
            if response.status_code == 401:
                self.log_result("Invalid Token Rejection", True, "Properly rejected")
            else:
                self.log_result("Invalid Token Rejection", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Invalid Token Rejection", False, f"Error: {e}")
        
        # Test missing token
        try:
            response = self.session.get(
                f"{BASE_URL}/api/users/me",
                timeout=10
            )
            
            if response.status_code == 401:
                self.log_result("Missing Token Rejection", True, "Properly rejected")
            else:
                self.log_result("Missing Token Rejection", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Missing Token Rejection", False, f"Error: {e}")
    
    def test_endpoint_access_control(self, token: str):
        """Test access control for different endpoints"""
        print("\n🛡️ Testing Endpoint Access Control...")
        
        if not token:
            self.log_result("Endpoint Access Control", False, "No token available")
            return
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test protected endpoints
        protected_endpoints = [
            ("GET", "/api/users/me", "User Profile"),
            ("GET", "/api/organizations", "Organizations List"),
            ("GET", "/api/projects", "Projects List"),
            ("GET", "/api/boards", "Boards List"),
            ("GET", "/api/ai-projects", "AI Projects List")
        ]
        
        for method, endpoint, description in protected_endpoints:
            try:
                response = self.session.request(
                    method=method,
                    url=f"{BASE_URL}{endpoint}",
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code in [200, 201]:
                    self.log_result(f"Access to {description}", True, f"Status: {response.status_code}")
                elif response.status_code == 401:
                    self.log_result(f"Access to {description}", False, "Unauthorized (token may be invalid)")
                else:
                    self.log_result(f"Access to {description}", False, f"Status: {response.status_code}")
                    
            except Exception as e:
                self.log_result(f"Access to {description}", False, f"Error: {e}")
    
    def test_data_filtering_by_role(self, token: str):
        """Test if data is properly filtered based on user role"""
        print("\n🔍 Testing Role-Based Data Filtering...")
        
        if not token:
            self.log_result("Role-Based Data Filtering", False, "No token available")
            return
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test organizations access
        try:
            response = self.session.get(
                f"{BASE_URL}/api/organizations",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if "organizations" in data and isinstance(data["organizations"], list):
                    org_count = len(data["organizations"])
                    self.log_result("Organizations Data Access", True, f"{org_count} organizations visible")
                else:
                    self.log_result("Organizations Data Access", False, "Invalid response format")
            else:
                self.log_result("Organizations Data Access", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Organizations Data Access", False, f"Error: {e}")
        
        # Test projects access
        try:
            response = self.session.get(
                f"{BASE_URL}/api/projects",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if "projects" in data and isinstance(data["projects"], list):
                    project_count = len(data["projects"])
                    self.log_result("Projects Data Access", True, f"{project_count} projects visible")
                else:
                    self.log_result("Projects Data Access", False, "Invalid response format")
            else:
                self.log_result("Projects Data Access", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Projects Data Access", False, f"Error: {e}")
    
    def test_unauthorized_access_attempts(self):
        """Test that unauthorized access is properly blocked"""
        print("\n🚫 Testing Unauthorized Access Prevention...")
        
        # Test access without authentication
        endpoints_to_test = [
            ("GET", "/api/users/me", "User Profile"),
            ("GET", "/api/organizations", "Organizations"),
            ("GET", "/api/projects", "Projects"),
            ("POST", "/api/projects", "Create Project"),
            ("GET", "/api/boards", "Boards")
        ]
        
        for method, endpoint, description in endpoints_to_test:
            try:
                response = self.session.request(
                    method=method,
                    url=f"{BASE_URL}{endpoint}",
                    timeout=10
                )
                
                if response.status_code == 401:
                    self.log_result(f"Unauthorized {description} blocked", True, "Properly rejected")
                elif response.status_code == 405:
                    self.log_result(f"Unauthorized {description} blocked", True, "Method not allowed (expected)")
                else:
                    self.log_result(f"Unauthorized {description} blocked", False, f"Status: {response.status_code}")
                    
            except Exception as e:
                self.log_result(f"Unauthorized {description} blocked", False, f"Error: {e}")
    
    def run_all_tests(self):
        """Run all RBAC tests"""
        print("🚀 Starting Role-Based Access Control Tests...")
        print("=" * 60)
        
        start_time = time.time()
        
        # Test authentication flow
        token = self.test_authentication_flow()
        
        # Test JWT validation
        self.test_jwt_token_validation(token)
        
        # Test endpoint access control
        self.test_endpoint_access_control(token)
        
        # Test data filtering
        self.test_data_filtering_by_role(token)
        
        # Test unauthorized access
        self.test_unauthorized_access_attempts()
        
        # Generate summary
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("📊 RBAC TEST SUMMARY")
        print("=" * 60)
        print(f"✅ Passed: {self.test_results['passed']}")
        print(f"❌ Failed: {self.test_results['failed']}")
        print(f"⏱️ Duration: {duration:.2f} seconds")
        
        if self.test_results["errors"]:
            print("\n🚨 FAILED TESTS:")
            for error in self.test_results["errors"]:
                print(f"  - {error}")
        
        success_rate = (self.test_results["passed"] / 
                       (self.test_results["passed"] + self.test_results["failed"])) * 100
        print(f"\n📈 Success Rate: {success_rate:.1f}%")
        
        return self.test_results

if __name__ == "__main__":
    tester = RBACTester()
    results = tester.run_all_tests()
