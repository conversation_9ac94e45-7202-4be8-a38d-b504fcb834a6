#!/usr/bin/env python3
"""
Final Production-Ready Enhanced Server
Fixed all async issues and production problems
"""
import asyncio
import logging
import time
import uuid
import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

import asyncpg
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException, Depends, Request, Response, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, EmailStr

# Simple security functions
import hashlib
import jwt

def hash_password(password: str) -> str:
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    return hash_password(password) == hashed

def create_access_token(data: dict) -> str:
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, "secret-key", algorithm="HS256")

def verify_token(token: str) -> dict:
    return jwt.decode(token, "secret-key", algorithms=["HS256"])

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = "postgresql://postgres:admin@localhost:5432/test_agnoworksphere"

# Global database pool
db_pool: Optional[asyncpg.Pool] = None

async def init_database():
    """Initialize database connection pool"""
    global db_pool
    try:
        db_pool = await asyncpg.create_pool(
            DATABASE_URL,
            min_size=5,
            max_size=10,
            command_timeout=30
        )
        logger.info("Database pool initialized successfully")
        
        # Test connection and initialize demo data
        async with db_pool.acquire() as conn:
            await conn.fetchval("SELECT 1")
            await initialize_demo_data(conn)
            
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

async def close_database():
    """Close database connection pool"""
    global db_pool
    if db_pool:
        await db_pool.close()
        logger.info("Database pool closed")

async def initialize_demo_data(conn):
    """Initialize demo data in database"""
    try:
        # Check if demo data already exists
        user_count = await conn.fetchval("SELECT COUNT(*) FROM users")
        if user_count > 0:
            logger.info("Demo data already exists")
            return
        
        logger.info("Initializing demo data...")
        
        # Create demo organization
        org_id = str(uuid.uuid4())
        await conn.execute("""
            INSERT INTO organizations (id, name, description, domain)
            VALUES ($1, $2, $3, $4)
        """, org_id, "ACME Corporation", "Demo organization", "acme.com")
        
        # Create demo users
        demo_users = [
            {"email": "<EMAIL>", "password": "Owner123!", "first_name": "John", "last_name": "Owner", "role": "owner"},
            {"email": "<EMAIL>", "password": "Admin123!", "first_name": "Jane", "last_name": "Admin", "role": "admin"},
            {"email": "<EMAIL>", "password": "Member123!", "first_name": "Bob", "last_name": "Member", "role": "member"},
            {"email": "<EMAIL>", "password": "Viewer123!", "first_name": "Alice", "last_name": "Viewer", "role": "viewer"}
        ]

        user_ids = []
        for user_data in demo_users:
            user_id = str(uuid.uuid4())
            user_ids.append(user_id)
            hashed_password = hash_password(user_data["password"])

            # Insert user
            await conn.execute("""
                INSERT INTO users (id, email, password_hash, first_name, last_name, email_verified)
                VALUES ($1, $2, $3, $4, $5, $6)
            """, user_id, user_data["email"], hashed_password, user_data["first_name"],
                user_data["last_name"], True)

            # Add to organization
            await conn.execute("""
                INSERT INTO organization_members (user_id, organization_id, role)
                VALUES ($1, $2, $3)
            """, user_id, org_id, user_data["role"])

        # Create demo projects with boards, columns, and cards
        await create_demo_projects_and_boards(conn, org_id, user_ids)

        logger.info("Demo data initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize demo data: {e}")

async def create_demo_projects_and_boards(conn, org_id, user_ids):
    """Create demo projects with complete board structure"""
    try:
        # Create demo projects
        projects_data = [
            {
                "name": "Website Redesign",
                "description": "Complete redesign of company website with modern UI/UX",
                "status": "active",
                "priority": "high",
                "progress": 65
            },
            {
                "name": "Mobile App Development",
                "description": "Native mobile app for iOS and Android platforms",
                "status": "active",
                "priority": "medium",
                "progress": 30
            },
            {
                "name": "Marketing Campaign",
                "description": "Q4 marketing campaign for product launch",
                "status": "active",
                "priority": "urgent",
                "progress": 80
            }
        ]

        for i, project_data in enumerate(projects_data):
            project_id = str(uuid.uuid4())

            # Create project
            await conn.execute("""
                INSERT INTO projects (id, name, description, organization_id, status, priority,
                                    progress, created_by, project_manager)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            """, project_id, project_data["name"], project_data["description"], org_id,
                project_data["status"], project_data["priority"], project_data["progress"],
                user_ids[0], user_ids[1])  # Owner creates, Admin manages

            # Create board for project
            board_id = str(uuid.uuid4())
            await conn.execute("""
                INSERT INTO boards (id, name, description, project_id, created_by)
                VALUES ($1, $2, $3, $4, $5)
            """, board_id, f"{project_data['name']} Board", f"Kanban board for {project_data['name']}",
                project_id, user_ids[0])

            # Create columns
            columns_data = [
                {"name": "To Do", "status": "todo", "position": 0},
                {"name": "In Progress", "status": "in_progress", "position": 1},
                {"name": "Review", "status": "review", "position": 2},
                {"name": "Done", "status": "done", "position": 3}
            ]

            column_ids = []
            for col_data in columns_data:
                column_id = str(uuid.uuid4())
                column_ids.append(column_id)

                await conn.execute("""
                    INSERT INTO columns (id, name, status, position, board_id, created_by)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, column_id, col_data["name"], col_data["status"], col_data["position"],
                    board_id, user_ids[0])

            # Create cards for each project
            await create_demo_cards(conn, project_id, column_ids, user_ids, i)

        logger.info("Demo projects, boards, and cards created successfully")

    except Exception as e:
        logger.error(f"Failed to create demo projects and boards: {e}")

async def create_demo_cards(conn, project_id, column_ids, user_ids, project_index):
    """Create demo cards with assignments and checklist items"""
    try:
        # Different cards for each project
        cards_data = [
            # Project 0 - Website Redesign
            [
                {"title": "Design Homepage Mockup", "description": "Create wireframes and mockups for new homepage", "column": 2, "priority": "high", "assigned_to": user_ids[1]},
                {"title": "Implement Responsive Layout", "description": "Code responsive CSS for all screen sizes", "column": 1, "priority": "medium", "assigned_to": user_ids[2]},
                {"title": "User Testing", "description": "Conduct user testing sessions", "column": 0, "priority": "low", "assigned_to": user_ids[3]},
                {"title": "Deploy to Production", "description": "Final deployment and go-live", "column": 3, "priority": "urgent", "assigned_to": user_ids[0]}
            ],
            # Project 1 - Mobile App
            [
                {"title": "Setup Development Environment", "description": "Configure React Native development setup", "column": 3, "priority": "high", "assigned_to": user_ids[1]},
                {"title": "Implement Authentication", "description": "User login and registration functionality", "column": 1, "priority": "high", "assigned_to": user_ids[2]},
                {"title": "Design App Icons", "description": "Create app icons for iOS and Android", "column": 0, "priority": "medium", "assigned_to": user_ids[3]},
                {"title": "API Integration", "description": "Connect app to backend APIs", "column": 0, "priority": "high", "assigned_to": user_ids[1]}
            ],
            # Project 2 - Marketing Campaign
            [
                {"title": "Social Media Content", "description": "Create content for social media platforms", "column": 2, "priority": "urgent", "assigned_to": user_ids[3]},
                {"title": "Email Campaign Setup", "description": "Configure email marketing automation", "column": 3, "priority": "high", "assigned_to": user_ids[2]},
                {"title": "Landing Page Optimization", "description": "A/B test landing page variations", "column": 1, "priority": "medium", "assigned_to": user_ids[1]},
                {"title": "Analytics Dashboard", "description": "Setup tracking and analytics", "column": 0, "priority": "low", "assigned_to": user_ids[0]}
            ]
        ]

        project_cards = cards_data[project_index] if project_index < len(cards_data) else cards_data[0]

        for i, card_data in enumerate(project_cards):
            card_id = str(uuid.uuid4())

            # Create card
            await conn.execute("""
                INSERT INTO cards (id, title, description, column_id, position, priority,
                                 created_by, assigned_to)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """, card_id, card_data["title"], card_data["description"],
                column_ids[card_data["column"]], i, card_data["priority"],
                user_ids[0], card_data["assigned_to"])

            # Create card assignment
            await conn.execute("""
                INSERT INTO card_assignments (card_id, user_id)
                VALUES ($1, $2)
            """, card_id, card_data["assigned_to"])

            # Create checklist items
            checklist_items = [
                f"Research requirements for {card_data['title']}",
                f"Create initial draft/prototype",
                f"Review and get feedback",
                f"Implement final version"
            ]

            for j, item_text in enumerate(checklist_items):
                await conn.execute("""
                    INSERT INTO checklist_items (card_id, text, completed, position)
                    VALUES ($1, $2, $3, $4)
                """, card_id, item_text, j < 2, j)  # First 2 items completed

        logger.info(f"Demo cards created for project {project_index}")

    except Exception as e:
        logger.error(f"Failed to create demo cards: {e}")

# Create FastAPI app
app = FastAPI(
    title="Agno WorkSphere Enhanced API",
    description="Final production-ready enhanced API",
    version="2.0.0"
)

# Add middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    await init_database()

@app.on_event("shutdown")
async def shutdown_event():
    await close_database()

# Exception handlers
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "message": str(exc)
        }
    )

# Dependency to get database connection
async def get_db():
    """Dependency to get database connection"""
    if not db_pool:
        raise HTTPException(status_code=500, detail="Database not initialized")
    
    async with db_pool.acquire() as conn:
        yield conn

# Pydantic models
class UserRegister(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: Optional[str] = ""
    organization_name: Optional[str] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class AIGeneratedProjectCreate(BaseModel):
    name: str
    description: str
    generated_tasks: dict
    organization_id: str

# API Routes
@app.get("/health")
async def health_check(db: asyncpg.Connection = Depends(get_db)):
    """Health check endpoint"""
    try:
        # Test database
        await db.fetchval("SELECT 1")
        user_count = await db.fetchval("SELECT COUNT(*) FROM users")
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "database": "connected",
            "cache": "disabled",
            "version": "2.0.0",
            "user_count": user_count
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "database": f"error: {str(e)}",
                "cache": "disabled",
                "version": "2.0.0"
            }
        )

@app.get("/health/detailed")
async def detailed_health_check(db: asyncpg.Connection = Depends(get_db)):
    """Detailed health check"""
    try:
        await db.fetchval("SELECT 1")
        user_count = await db.fetchval("SELECT COUNT(*) FROM users")
        org_count = await db.fetchval("SELECT COUNT(*) FROM organizations")
        
        return {
            "overall_status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "database": {
                    "status": "healthy",
                    "user_count": user_count,
                    "organization_count": org_count
                },
                "cache": {
                    "status": "disabled"
                }
            }
        }
        
    except Exception as e:
        return {
            "overall_status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "database": {
                    "status": "unhealthy",
                    "error": str(e)
                },
                "cache": {
                    "status": "disabled"
                }
            }
        }

@app.post("/api/auth/register")
async def register_user(user_data: UserRegister, db: asyncpg.Connection = Depends(get_db)):
    """Register a new user"""
    try:
        # Check if user exists
        existing = await db.fetchrow("SELECT id FROM users WHERE email = $1", user_data.email)
        if existing:
            raise HTTPException(status_code=400, detail="User already exists")
        
        # Generate UUID and hash password
        user_id = str(uuid.uuid4())
        hashed_password = hash_password(user_data.password)
        
        # Create user
        await db.execute("""
            INSERT INTO users (id, email, password_hash, first_name, last_name, email_verified)
            VALUES ($1, $2, $3, $4, $5, $6)
        """, user_id, user_data.email, hashed_password, user_data.first_name, 
            user_data.last_name or "", True)
        
        logger.info(f"User registered: {user_data.email}")
        
        return {
            "success": True,
            "message": "User registered successfully",
            "user_id": user_id,
            "email": user_data.email
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration failed: {e}")
        raise HTTPException(status_code=500, detail=f"Registration failed: {str(e)}")

@app.post("/api/auth/login")
async def login_user(login_data: UserLogin, db: asyncpg.Connection = Depends(get_db)):
    """Login user"""
    try:
        user = await db.fetchrow("""
            SELECT u.id, u.email, u.first_name, u.last_name, u.password_hash,
                   om.role
            FROM users u
            LEFT JOIN organization_members om ON u.id = om.user_id
            WHERE u.email = $1
            LIMIT 1
        """, login_data.email)
        
        if not user or not verify_password(login_data.password, user['password_hash']):
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # Generate token
        token_data = {"sub": str(user['id']), "email": user['email']}
        token = create_access_token(token_data)
        
        logger.info(f"User logged in: {user['email']}")
        
        return {
            "success": True,
            "access_token": token,
            "token_type": "bearer",
            "user": {
                "id": str(user['id']),
                "email": user['email'],
                "first_name": user['first_name'],
                "last_name": user['last_name'],
                "role": user['role'] or "member"
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login failed: {e}")
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@app.get("/api/organizations")
async def get_organizations(db: asyncpg.Connection = Depends(get_db)):
    """Get organizations"""
    try:
        orgs = await db.fetch("""
            SELECT o.id, o.name, o.description, o.domain, o.created_at,
                   COUNT(om.user_id) as member_count
            FROM organizations o
            LEFT JOIN organization_members om ON o.id = om.organization_id
            GROUP BY o.id, o.name, o.description, o.domain, o.created_at
            ORDER BY o.created_at DESC
        """)
        
        organizations = []
        for org in orgs:
            organizations.append({
                "id": str(org['id']),
                "name": org['name'],
                "description": org['description'],
                "domain": org['domain'],
                "created_at": org['created_at'].isoformat(),
                "member_count": org['member_count']
            })
        
        return organizations
        
    except Exception as e:
        logger.error(f"Failed to get organizations: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get organizations: {str(e)}")

@app.get("/api/projects")
async def get_projects(db: asyncpg.Connection = Depends(get_db)):
    """Get projects"""
    try:
        projects = await db.fetch("""
            SELECT id, name, description, organization_id, status, priority, 
                   progress, created_at
            FROM projects 
            ORDER BY created_at DESC
        """)
        
        result = []
        for project in projects:
            result.append({
                "id": str(project['id']),
                "name": project['name'],
                "description": project['description'],
                "organization_id": str(project['organization_id']),
                "status": project['status'],
                "priority": project['priority'],
                "progress": project['progress'],
                "created_at": project['created_at'].isoformat()
            })
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to get projects: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get projects: {str(e)}")

@app.get("/api/ai-projects")
async def get_ai_projects(db: asyncpg.Connection = Depends(get_db)):
    """Get AI-generated projects"""
    try:
        projects = await db.fetch("""
            SELECT id, name, description, generated_tasks, organization_id, 
                   created_by, created_at
            FROM ai_generated_projects 
            ORDER BY created_at DESC
        """)
        
        result = []
        for project in projects:
            result.append({
                "id": str(project['id']),
                "name": project['name'],
                "description": project['description'],
                "generated_tasks": json.loads(project['generated_tasks']),
                "organization_id": str(project['organization_id']),
                "created_by": str(project['created_by']),
                "created_at": project['created_at'].isoformat()
            })
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to get AI projects: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get AI projects: {str(e)}")

@app.get("/api/users/me")
async def get_current_user(authorization: Optional[str] = Header(None), db: asyncpg.Connection = Depends(get_db)):
    """Get current user profile"""
    try:
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header required")

        # Extract token
        token = authorization.replace("Bearer ", "")
        payload = verify_token(token)
        user_id = payload.get("sub")

        if not user_id:
            raise HTTPException(status_code=401, detail="Invalid token")

        # Get user from database
        user = await db.fetchrow("""
            SELECT u.id, u.email, u.first_name, u.last_name, u.email_verified,
                   u.created_at, om.role
            FROM users u
            LEFT JOIN organization_members om ON u.id = om.user_id
            WHERE u.id = $1
            LIMIT 1
        """, user_id)

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        return {
            "id": str(user['id']),
            "email": user['email'],
            "first_name": user['first_name'],
            "last_name": user['last_name'],
            "email_verified": user['email_verified'],
            "created_at": user['created_at'].isoformat(),
            "role": user['role'] or "member"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get current user: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get user: {str(e)}")

@app.get("/metrics")
async def get_metrics(db: asyncpg.Connection = Depends(get_db)):
    """Get application metrics"""
    try:
        db_stats = await db.fetchrow("""
            SELECT
                (SELECT COUNT(*) FROM users) as user_count,
                (SELECT COUNT(*) FROM organizations) as org_count,
                (SELECT COUNT(*) FROM projects) as project_count,
                (SELECT COUNT(*) FROM ai_generated_projects) as ai_project_count,
                (SELECT COUNT(*) FROM boards) as board_count,
                (SELECT COUNT(*) FROM cards) as card_count,
                (SELECT COUNT(*) FROM card_assignments) as assignment_count
        """)

        return {
            "database": dict(db_stats),
            "pool_stats": {
                "size": db_pool.get_size() if db_pool else 0,
                "idle": db_pool.get_idle_size() if db_pool else 0
            },
            "cache_stats": {"hits": 0, "misses": 0, "errors": 0},
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")

@app.get("/api/projects/{project_id}/boards")
async def get_project_boards(project_id: str, db: asyncpg.Connection = Depends(get_db)):
    """Get boards for a specific project"""
    try:
        boards = await db.fetch("""
            SELECT b.id, b.name, b.description, b.project_id, b.created_by, b.created_at,
                   COUNT(c.id) as column_count
            FROM boards b
            LEFT JOIN columns c ON b.id = c.board_id
            WHERE b.project_id = $1
            GROUP BY b.id, b.name, b.description, b.project_id, b.created_by, b.created_at
            ORDER BY b.created_at DESC
        """, project_id)

        result = []
        for board in boards:
            result.append({
                "id": str(board['id']),
                "name": board['name'],
                "description": board['description'],
                "project_id": str(board['project_id']),
                "created_by": str(board['created_by']),
                "created_at": board['created_at'].isoformat(),
                "column_count": board['column_count']
            })

        return {"success": True, "boards": result}

    except Exception as e:
        logger.error(f"Failed to get project boards: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get boards: {str(e)}")

@app.get("/api/boards/{board_id}/columns")
async def get_board_columns(board_id: str, db: asyncpg.Connection = Depends(get_db)):
    """Get columns for a specific board"""
    try:
        columns = await db.fetch("""
            SELECT c.id, c.name, c.status, c.position, c.board_id, c.created_by, c.created_at,
                   COUNT(ca.id) as card_count
            FROM columns c
            LEFT JOIN cards ca ON c.id = ca.column_id
            WHERE c.board_id = $1
            GROUP BY c.id, c.name, c.status, c.position, c.board_id, c.created_by, c.created_at
            ORDER BY c.position ASC
        """, board_id)

        result = []
        for column in columns:
            result.append({
                "id": str(column['id']),
                "name": column['name'],
                "status": column['status'],
                "position": column['position'],
                "board_id": str(column['board_id']),
                "created_by": str(column['created_by']),
                "created_at": column['created_at'].isoformat(),
                "card_count": column['card_count']
            })

        return {"success": True, "columns": result}

    except Exception as e:
        logger.error(f"Failed to get board columns: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get columns: {str(e)}")

@app.get("/api/columns/{column_id}/cards")
async def get_column_cards(column_id: str, db: asyncpg.Connection = Depends(get_db)):
    """Get cards for a specific column"""
    try:
        cards = await db.fetch("""
            SELECT c.id, c.title, c.description, c.column_id, c.position, c.priority,
                   c.due_date, c.created_by, c.assigned_to, c.created_at, c.updated_at,
                   u1.first_name as creator_first_name, u1.last_name as creator_last_name,
                   u2.first_name as assignee_first_name, u2.last_name as assignee_last_name,
                   COUNT(ci.id) as checklist_count,
                   COUNT(CASE WHEN ci.completed = true THEN 1 END) as completed_items
            FROM cards c
            LEFT JOIN users u1 ON c.created_by = u1.id
            LEFT JOIN users u2 ON c.assigned_to = u2.id
            LEFT JOIN checklist_items ci ON c.id = ci.card_id
            WHERE c.column_id = $1
            GROUP BY c.id, c.title, c.description, c.column_id, c.position, c.priority,
                     c.due_date, c.created_by, c.assigned_to, c.created_at, c.updated_at,
                     u1.first_name, u1.last_name, u2.first_name, u2.last_name
            ORDER BY c.position ASC
        """, column_id)

        result = []
        for card in cards:
            result.append({
                "id": str(card['id']),
                "title": card['title'],
                "description": card['description'],
                "column_id": str(card['column_id']),
                "position": card['position'],
                "priority": card['priority'],
                "due_date": card['due_date'].isoformat() if card['due_date'] else None,
                "created_by": str(card['created_by']),
                "assigned_to": str(card['assigned_to']) if card['assigned_to'] else None,
                "created_at": card['created_at'].isoformat(),
                "updated_at": card['updated_at'].isoformat(),
                "creator_name": f"{card['creator_first_name']} {card['creator_last_name']}",
                "assignee_name": f"{card['assignee_first_name']} {card['assignee_last_name']}" if card['assignee_first_name'] else None,
                "checklist_count": card['checklist_count'],
                "completed_items": card['completed_items'],
                "completion_percentage": round((card['completed_items'] / card['checklist_count'] * 100) if card['checklist_count'] > 0 else 0)
            })

        return {"success": True, "cards": result}

    except Exception as e:
        logger.error(f"Failed to get column cards: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get cards: {str(e)}")

@app.get("/api/cards/{card_id}/checklist")
async def get_card_checklist(card_id: str, db: asyncpg.Connection = Depends(get_db)):
    """Get checklist items for a specific card"""
    try:
        items = await db.fetch("""
            SELECT id, card_id, text, completed, position, created_at, updated_at
            FROM checklist_items
            WHERE card_id = $1
            ORDER BY position ASC
        """, card_id)

        result = []
        for item in items:
            result.append({
                "id": str(item['id']),
                "card_id": str(item['card_id']),
                "text": item['text'],
                "completed": item['completed'],
                "position": item['position'],
                "created_at": item['created_at'].isoformat(),
                "updated_at": item['updated_at'].isoformat()
            })

        return {"success": True, "checklist_items": result}

    except Exception as e:
        logger.error(f"Failed to get card checklist: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get checklist: {str(e)}")

@app.get("/api/cards/{card_id}/assignments")
async def get_card_assignments(card_id: str, db: asyncpg.Connection = Depends(get_db)):
    """Get assignments for a specific card"""
    try:
        assignments = await db.fetch("""
            SELECT ca.id, ca.card_id, ca.user_id, ca.assigned_at,
                   u.first_name, u.last_name, u.email
            FROM card_assignments ca
            JOIN users u ON ca.user_id = u.id
            WHERE ca.card_id = $1
            ORDER BY ca.assigned_at DESC
        """, card_id)

        result = []
        for assignment in assignments:
            result.append({
                "id": str(assignment['id']),
                "card_id": str(assignment['card_id']),
                "user_id": str(assignment['user_id']),
                "assigned_at": assignment['assigned_at'].isoformat(),
                "user_name": f"{assignment['first_name']} {assignment['last_name']}",
                "user_email": assignment['email']
            })

        return {"success": True, "assignments": result}

    except Exception as e:
        logger.error(f"Failed to get card assignments: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get assignments: {str(e)}")

@app.get("/api/boards")
async def get_all_boards(db: asyncpg.Connection = Depends(get_db)):
    """Get all boards with project information"""
    try:
        boards = await db.fetch("""
            SELECT b.id, b.name, b.description, b.project_id, b.created_by, b.created_at,
                   p.name as project_name, p.status as project_status,
                   COUNT(c.id) as column_count
            FROM boards b
            JOIN projects p ON b.project_id = p.id
            LEFT JOIN columns c ON b.id = c.board_id
            GROUP BY b.id, b.name, b.description, b.project_id, b.created_by, b.created_at,
                     p.name, p.status
            ORDER BY b.created_at DESC
        """)

        result = []
        for board in boards:
            result.append({
                "id": str(board['id']),
                "name": board['name'],
                "description": board['description'],
                "project_id": str(board['project_id']),
                "project_name": board['project_name'],
                "project_status": board['project_status'],
                "created_by": str(board['created_by']),
                "created_at": board['created_at'].isoformat(),
                "column_count": board['column_count']
            })

        return {"success": True, "boards": result}

    except Exception as e:
        logger.error(f"Failed to get all boards: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get boards: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "enhanced_server:app",
        host="0.0.0.0",
        port=3001,
        reload=False,
        log_level="info"
    )
