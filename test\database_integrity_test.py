#!/usr/bin/env python3
"""
Database Integrity Test for Agno WorkSphere
Verifies database structure, data consistency, and JSONB functionality
"""
import asyncio
import asyncpg
import json
from typing import Dict, List, Any, Optional

DATABASE_URL = "postgresql://postgres:admin@localhost:5432/test_agnoworksphere"

class DatabaseIntegrityTest:
    def __init__(self):
        self.connection = None
        self.test_results = {
            "passed": 0,
            "failed": 0,
            "errors": []
        }
    
    async def connect(self):
        """Connect to the database"""
        try:
            self.connection = await asyncpg.connect(DATABASE_URL)
            print("✅ Database connection established")
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from the database"""
        if self.connection:
            await self.connection.close()
            print("✅ Database connection closed")
    
    def log_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        if success:
            self.test_results["passed"] += 1
            print(f"✅ {test_name}: PASSED {details}")
        else:
            self.test_results["failed"] += 1
            self.test_results["errors"].append(f"{test_name}: {details}")
            print(f"❌ {test_name}: FAILED {details}")
    
    async def test_table_existence(self):
        """Test if all required tables exist"""
        print("\n🗄️ Testing Table Existence...")
        
        required_tables = [
            "users", "organizations", "projects", "boards", "columns", 
            "cards", "checklist_items", "card_assignments", "ai_generated_projects"
        ]
        
        for table in required_tables:
            try:
                result = await self.connection.fetchval("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = $1
                    )
                """, table)
                
                if result:
                    self.log_result(f"Table {table}", True, "exists")
                else:
                    self.log_result(f"Table {table}", False, "does not exist")
                    
            except Exception as e:
                self.log_result(f"Table {table}", False, f"error checking: {e}")
    
    async def test_table_population(self):
        """Test if tables are properly populated with data"""
        print("\n📊 Testing Table Population...")
        
        tables_to_check = [
            ("users", "User accounts"),
            ("organizations", "Organizations"),
            ("projects", "Projects"),
            ("boards", "Kanban boards"),
            ("columns", "Board columns"),
            ("cards", "Task cards"),
            ("checklist_items", "Checklist items"),
            ("ai_generated_projects", "AI-generated projects")
        ]
        
        for table, description in tables_to_check:
            try:
                count = await self.connection.fetchval(f"SELECT COUNT(*) FROM {table}")
                if count > 0:
                    self.log_result(f"{description} population", True, f"{count} records")
                else:
                    self.log_result(f"{description} population", False, "no records found")
                    
            except Exception as e:
                self.log_result(f"{description} population", False, f"error: {e}")
    
    async def test_foreign_key_relationships(self):
        """Test foreign key relationships and data consistency"""
        print("\n🔗 Testing Foreign Key Relationships...")
        
        # Test organization-project relationship
        try:
            orphaned_projects = await self.connection.fetchval("""
                SELECT COUNT(*) FROM projects p 
                LEFT JOIN organizations o ON p.organization_id = o.id 
                WHERE o.id IS NULL
            """)
            
            if orphaned_projects == 0:
                self.log_result("Project-Organization FK", True, "no orphaned projects")
            else:
                self.log_result("Project-Organization FK", False, f"{orphaned_projects} orphaned projects")
                
        except Exception as e:
            self.log_result("Project-Organization FK", False, f"error: {e}")
        
        # Test project-board relationship
        try:
            orphaned_boards = await self.connection.fetchval("""
                SELECT COUNT(*) FROM boards b 
                LEFT JOIN projects p ON b.project_id = p.id 
                WHERE p.id IS NULL
            """)
            
            if orphaned_boards == 0:
                self.log_result("Board-Project FK", True, "no orphaned boards")
            else:
                self.log_result("Board-Project FK", False, f"{orphaned_boards} orphaned boards")
                
        except Exception as e:
            self.log_result("Board-Project FK", False, f"error: {e}")
        
        # Test board-column relationship
        try:
            orphaned_columns = await self.connection.fetchval("""
                SELECT COUNT(*) FROM columns c 
                LEFT JOIN boards b ON c.board_id = b.id 
                WHERE b.id IS NULL
            """)
            
            if orphaned_columns == 0:
                self.log_result("Column-Board FK", True, "no orphaned columns")
            else:
                self.log_result("Column-Board FK", False, f"{orphaned_columns} orphaned columns")
                
        except Exception as e:
            self.log_result("Column-Board FK", False, f"error: {e}")
        
        # Test column-card relationship
        try:
            orphaned_cards = await self.connection.fetchval("""
                SELECT COUNT(*) FROM cards c 
                LEFT JOIN columns col ON c.column_id = col.id 
                WHERE col.id IS NULL
            """)
            
            if orphaned_cards == 0:
                self.log_result("Card-Column FK", True, "no orphaned cards")
            else:
                self.log_result("Card-Column FK", False, f"{orphaned_cards} orphaned cards")
                
        except Exception as e:
            self.log_result("Card-Column FK", False, f"error: {e}")
    
    async def test_jsonb_functionality(self):
        """Test JSONB data structure in ai_generated_projects table"""
        print("\n🤖 Testing JSONB Functionality...")
        
        try:
            # Check if ai_generated_projects has JSONB column
            column_info = await self.connection.fetchrow("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'ai_generated_projects' 
                AND column_name = 'generated_tasks'
            """)
            
            if column_info and column_info['data_type'] == 'jsonb':
                self.log_result("JSONB column exists", True, "generated_tasks is JSONB")
            else:
                self.log_result("JSONB column exists", False, "generated_tasks not found or not JSONB")
                return
            
            # Test JSONB data structure
            sample_projects = await self.connection.fetch("""
                SELECT id, name, generated_tasks 
                FROM ai_generated_projects 
                LIMIT 3
            """)
            
            valid_jsonb_count = 0
            for project in sample_projects:
                try:
                    tasks = project['generated_tasks']
                    if isinstance(tasks, (dict, list)):
                        valid_jsonb_count += 1
                        
                        # Check if tasks have required structure
                        if isinstance(tasks, list) and len(tasks) > 0:
                            first_task = tasks[0]
                            if isinstance(first_task, dict) and 'title' in first_task:
                                self.log_result(f"JSONB structure for {project['name']}", True, "valid task structure")
                            else:
                                self.log_result(f"JSONB structure for {project['name']}", False, "invalid task structure")
                        else:
                            self.log_result(f"JSONB structure for {project['name']}", True, "valid JSONB format")
                            
                except Exception as e:
                    self.log_result(f"JSONB parsing for {project['name']}", False, f"error: {e}")
            
            if valid_jsonb_count > 0:
                self.log_result("JSONB data validity", True, f"{valid_jsonb_count} valid JSONB records")
            else:
                self.log_result("JSONB data validity", False, "no valid JSONB records found")
                
        except Exception as e:
            self.log_result("JSONB functionality", False, f"error: {e}")
    
    async def test_data_consistency(self):
        """Test data consistency across related tables"""
        print("\n🔍 Testing Data Consistency...")
        
        try:
            # Test user-organization consistency
            user_org_consistency = await self.connection.fetchval("""
                SELECT COUNT(*) FROM users u
                JOIN organizations o ON u.id = ANY(
                    SELECT jsonb_array_elements_text(o.members::jsonb)::uuid
                ) WHERE u.id IS NOT NULL
            """)
            
            total_users = await self.connection.fetchval("SELECT COUNT(*) FROM users")
            
            if user_org_consistency > 0:
                self.log_result("User-Organization consistency", True, f"{user_org_consistency}/{total_users} users linked")
            else:
                self.log_result("User-Organization consistency", False, "no user-organization links found")
                
        except Exception as e:
            self.log_result("User-Organization consistency", False, f"error: {e}")
        
        # Test project status values
        try:
            invalid_statuses = await self.connection.fetchval("""
                SELECT COUNT(*) FROM projects 
                WHERE status NOT IN ('planning', 'active', 'on_hold', 'completed', 'cancelled')
            """)
            
            if invalid_statuses == 0:
                self.log_result("Project status consistency", True, "all statuses valid")
            else:
                self.log_result("Project status consistency", False, f"{invalid_statuses} invalid statuses")
                
        except Exception as e:
            self.log_result("Project status consistency", False, f"error: {e}")
    
    async def test_query_performance(self):
        """Test basic query performance"""
        print("\n⚡ Testing Query Performance...")
        
        import time
        
        # Test complex join query performance
        try:
            start_time = time.time()
            
            result = await self.connection.fetch("""
                SELECT p.name as project_name, b.name as board_name, 
                       COUNT(c.id) as card_count
                FROM projects p
                LEFT JOIN boards b ON p.id = b.project_id
                LEFT JOIN columns col ON b.id = col.board_id
                LEFT JOIN cards c ON col.id = c.column_id
                GROUP BY p.id, p.name, b.id, b.name
                ORDER BY p.name
            """)
            
            end_time = time.time()
            query_time = end_time - start_time
            
            if query_time < 1.0:  # Less than 1 second
                self.log_result("Complex query performance", True, f"{query_time:.3f}s for {len(result)} results")
            else:
                self.log_result("Complex query performance", False, f"{query_time:.3f}s (too slow)")
                
        except Exception as e:
            self.log_result("Complex query performance", False, f"error: {e}")
    
    async def run_all_tests(self):
        """Run all database integrity tests"""
        print("🚀 Starting Database Integrity Tests...")
        print("=" * 60)
        
        if not await self.connect():
            return {"error": "Could not connect to database"}
        
        try:
            await self.test_table_existence()
            await self.test_table_population()
            await self.test_foreign_key_relationships()
            await self.test_jsonb_functionality()
            await self.test_data_consistency()
            await self.test_query_performance()
            
        finally:
            await self.disconnect()
        
        # Generate summary
        print("\n" + "=" * 60)
        print("📊 DATABASE INTEGRITY SUMMARY")
        print("=" * 60)
        print(f"✅ Passed: {self.test_results['passed']}")
        print(f"❌ Failed: {self.test_results['failed']}")
        
        if self.test_results["errors"]:
            print("\n🚨 FAILED TESTS:")
            for error in self.test_results["errors"]:
                print(f"  - {error}")
        
        success_rate = (self.test_results["passed"] / 
                       (self.test_results["passed"] + self.test_results["failed"])) * 100
        print(f"\n📈 Success Rate: {success_rate:.1f}%")
        
        return self.test_results

async def main():
    tester = DatabaseIntegrityTest()
    results = await tester.run_all_tests()
    return results

if __name__ == "__main__":
    asyncio.run(main())
