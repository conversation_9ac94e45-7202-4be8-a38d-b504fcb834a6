#!/usr/bin/env python3
"""
Comprehensive API Testing Script for Agno WorkSphere
Tests all endpoints, authentication, role-based access, and data integrity
"""
import requests
import json
import time
from typing import Dict, Any, Optional

# Configuration
BASE_URL = "http://localhost:3001"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "testpassword123"

class APITester:
    def __init__(self):
        self.session = requests.Session()
        self.auth_token = None
        self.test_results = {
            "passed": 0,
            "failed": 0,
            "errors": []
        }
    
    def log_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        if success:
            self.test_results["passed"] += 1
            print(f"✅ {test_name}: PASSED {details}")
        else:
            self.test_results["failed"] += 1
            self.test_results["errors"].append(f"{test_name}: {details}")
            print(f"❌ {test_name}: FAILED {details}")
    
    def test_endpoint(self, method: str, endpoint: str, expected_status: int = 200, 
                     data: Dict = None, headers: Dict = None, test_name: str = None) -> Optional[Dict]:
        """Generic endpoint testing function"""
        if not test_name:
            test_name = f"{method} {endpoint}"
        
        try:
            url = f"{BASE_URL}{endpoint}"
            
            # Add auth headers if token exists
            if self.auth_token and headers is None:
                headers = {"Authorization": f"Bearer {self.auth_token}"}
            elif self.auth_token and headers:
                headers["Authorization"] = f"Bearer {self.auth_token}"
            
            response = self.session.request(
                method=method,
                url=url,
                json=data,
                headers=headers,
                timeout=10
            )
            
            # Check status code
            if response.status_code == expected_status:
                try:
                    response_data = response.json()
                    self.log_result(test_name, True, f"Status: {response.status_code}")
                    return response_data
                except json.JSONDecodeError:
                    self.log_result(test_name, True, f"Status: {response.status_code} (Non-JSON response)")
                    return {"text": response.text}
            else:
                self.log_result(test_name, False, f"Expected {expected_status}, got {response.status_code}")
                return None
                
        except Exception as e:
            self.log_result(test_name, False, f"Exception: {str(e)}")
            return None
    
    def test_health_endpoints(self):
        """Test health and monitoring endpoints"""
        print("\n🔍 Testing Health & Monitoring Endpoints...")
        
        # Basic health check
        self.test_endpoint("GET", "/health", 200, test_name="Health Check")
        
        # Detailed health check
        self.test_endpoint("GET", "/health/detailed", 200, test_name="Detailed Health Check")
        
        # Metrics endpoint
        self.test_endpoint("GET", "/metrics", 200, test_name="Metrics Endpoint")
    
    def test_authentication_endpoints(self):
        """Test authentication endpoints"""
        print("\n🔐 Testing Authentication Endpoints...")

        # Test registration
        registration_data = {
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD,
            "first_name": "Test",
            "last_name": "User",
            "organization_name": "Test Organization"
        }

        register_response = self.test_endpoint(
            "POST", "/api/auth/register", 201,
            registration_data, test_name="User Registration"
        )

        # Test login
        login_data = {
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        }

        login_response = self.test_endpoint(
            "POST", "/api/auth/login", 200,
            login_data, test_name="User Login"
        )

        # Extract auth token if login successful
        if login_response and "access_token" in login_response:
            self.auth_token = login_response["access_token"]
            print(f"🔑 Auth token obtained: {self.auth_token[:20]}...")

        # Test current user endpoint
        self.test_endpoint("GET", "/api/users/me", 200, test_name="Get Current User")
    
    def test_organization_endpoints(self):
        """Test organization endpoints"""
        print("\n🏢 Testing Organization Endpoints...")

        # Get all organizations
        orgs_response = self.test_endpoint("GET", "/api/organizations", 200, test_name="Get Organizations")

        if orgs_response and "organizations" in orgs_response:
            orgs = orgs_response["organizations"]
            if orgs:
                org_id = orgs[0]["id"]
                # Test get organization by ID
                self.test_endpoint("GET", f"/api/organizations/{org_id}", 200, test_name="Get Organization by ID")

    def test_project_endpoints(self):
        """Test project endpoints"""
        print("\n📋 Testing Project Endpoints...")

        # Get all projects
        projects_response = self.test_endpoint("GET", "/api/projects", 200, test_name="Get Projects")

        if projects_response and "projects" in projects_response:
            projects = projects_response["projects"]
            if projects:
                project_id = projects[0]["id"]
                # Test get project by ID
                self.test_endpoint("GET", f"/api/projects/{project_id}", 200, test_name="Get Project by ID")

    def test_ai_projects_endpoints(self):
        """Test AI projects endpoints"""
        print("\n🤖 Testing AI Projects Endpoints...")

        # Get AI projects
        self.test_endpoint("GET", "/api/ai-projects", 200, test_name="Get AI Projects")
    
    def test_kanban_endpoints(self):
        """Test Kanban board system endpoints"""
        print("\n📊 Testing Kanban Board Endpoints...")

        # Get all boards
        boards_response = self.test_endpoint("GET", "/api/boards", 200, test_name="Get All Boards")

        if boards_response and "boards" in boards_response:
            boards = boards_response["boards"]
            if boards:
                board_id = boards[0]["id"]

                # Test board columns
                columns_response = self.test_endpoint(
                    "GET", f"/api/boards/{board_id}/columns", 200,
                    test_name="Get Board Columns"
                )

                if columns_response and "columns" in columns_response:
                    columns = columns_response["columns"]
                    if columns:
                        column_id = columns[0]["id"]

                        # Test column cards
                        self.test_endpoint(
                            "GET", f"/api/columns/{column_id}/cards", 200,
                            test_name="Get Column Cards"
                        )
    
    def test_error_handling(self):
        """Test error handling and edge cases"""
        print("\n⚠️ Testing Error Handling...")

        # Test non-existent endpoints
        self.test_endpoint("GET", "/api/nonexistent", 404, test_name="Non-existent Endpoint")

        # Test invalid authentication
        old_token = self.auth_token
        self.auth_token = "invalid_token"
        self.test_endpoint("GET", "/api/users/me", 401, test_name="Invalid Auth Token")
        self.auth_token = old_token

        # Test invalid data
        self.test_endpoint(
            "POST", "/api/auth/login", 400,
            {"invalid": "data"}, test_name="Invalid Login Data"
        )
    
    def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting Comprehensive API Testing...")
        print("=" * 60)
        
        start_time = time.time()
        
        # Run test suites
        self.test_health_endpoints()
        self.test_authentication_endpoints()
        self.test_organization_endpoints()
        self.test_project_endpoints()
        self.test_ai_projects_endpoints()
        self.test_kanban_endpoints()
        self.test_error_handling()
        
        # Print summary
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"✅ Passed: {self.test_results['passed']}")
        print(f"❌ Failed: {self.test_results['failed']}")
        print(f"⏱️ Duration: {duration:.2f} seconds")
        
        if self.test_results["errors"]:
            print("\n🚨 FAILED TESTS:")
            for error in self.test_results["errors"]:
                print(f"  - {error}")
        
        success_rate = (self.test_results["passed"] / 
                       (self.test_results["passed"] + self.test_results["failed"])) * 100
        print(f"\n📈 Success Rate: {success_rate:.1f}%")
        
        return self.test_results

if __name__ == "__main__":
    tester = APITester()
    results = tester.run_all_tests()
