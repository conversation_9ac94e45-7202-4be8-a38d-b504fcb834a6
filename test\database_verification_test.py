#!/usr/bin/env python3
"""
Database verification test for Agno WorkSphere
Verifies that all data is properly stored and retrieved from the database
"""
import requests
import json
import time
from datetime import datetime, timedelta

class DatabaseVerificationTest:
    def __init__(self):
        self.base_url = "http://localhost:3001/api/v1"
        self.test_results = []
        self.test_data = {}
        self.auth_token = None
        
    def log_result(self, test_name, status, details=""):
        """Log test results"""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_emoji} {test_name}: {status}")
        if details:
            print(f"   Details: {details}")
    
    def make_request(self, method, endpoint, data=None, headers=None, params=None):
        """Make HTTP request"""
        url = f"{self.base_url}{endpoint}"
        default_headers = {"Content-Type": "application/json"}
        if headers:
            default_headers.update(headers)
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=default_headers, params=params)
            elif method.upper() == "POST":
                response = requests.post(url, headers=default_headers, json=data, params=params)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=default_headers, json=data, params=params)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=default_headers, params=params)
            
            return response
        except Exception as e:
            print(f"Request failed: {e}")
            raise
    
    def setup_authentication(self):
        """Setup authentication for testing"""
        try:
            # Try to register a test user
            reg_data = {
                "email": "<EMAIL>",
                "password": "testpass123",
                "full_name": "Database Test User"
            }
            
            reg_response = self.make_request("POST", "/auth/register", reg_data)
            
            # Try to login
            login_data = {
                "username": "<EMAIL>",
                "password": "testpass123"
            }
            
            login_response = self.make_request("POST", "/auth/login", login_data)
            
            if login_response.status_code == 200:
                token_data = login_response.json()
                self.auth_token = token_data.get("access_token")
                self.log_result("Authentication Setup", "PASS", "Token obtained")
                return True
            else:
                self.log_result("Authentication Setup", "FAIL", 
                              f"Login failed: {login_response.status_code}")
                return False
                
        except Exception as e:
            self.log_result("Authentication Setup", "FAIL", str(e))
            return False
    
    def test_organization_persistence(self):
        """Test organization data persistence"""
        if not self.auth_token:
            self.log_result("Organization Persistence", "SKIP", "No auth token")
            return
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            # Create organization
            org_data = {
                "name": "Test DB Organization",
                "description": "Testing database persistence",
                "industry": "Technology",
                "size": "small",
                "contact_email": "<EMAIL>"
            }
            
            create_response = self.make_request("POST", "/organizations", org_data, headers)
            
            if create_response.status_code in [200, 201]:
                org_response = create_response.json()
                org_id = org_response.get("id")
                self.test_data["organization_id"] = org_id
                
                # Verify data persistence by retrieving
                get_response = self.make_request("GET", f"/organizations/{org_id}", headers=headers)
                
                if get_response.status_code == 200:
                    retrieved_org = get_response.json()
                    if (retrieved_org.get("name") == org_data["name"] and 
                        retrieved_org.get("description") == org_data["description"]):
                        self.log_result("Organization Persistence", "PASS", 
                                      f"Data correctly stored and retrieved: {org_id}")
                    else:
                        self.log_result("Organization Persistence", "FAIL", 
                                      "Retrieved data doesn't match created data")
                else:
                    self.log_result("Organization Persistence", "FAIL", 
                                  f"Failed to retrieve: {get_response.status_code}")
            else:
                self.log_result("Organization Persistence", "FAIL", 
                              f"Creation failed: {create_response.status_code}")
                
        except Exception as e:
            self.log_result("Organization Persistence", "FAIL", str(e))
    
    def test_project_persistence(self):
        """Test project data persistence"""
        if not self.auth_token or "organization_id" not in self.test_data:
            self.log_result("Project Persistence", "SKIP", "Prerequisites not met")
            return
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            # Create project
            project_data = {
                "name": "Test DB Project",
                "description": "Testing project database persistence",
                "status": "active",
                "priority": "high",
                "start_date": datetime.now().date().isoformat(),
                "due_date": (datetime.now() + timedelta(days=30)).date().isoformat()
            }
            
            params = {"organization_id": self.test_data["organization_id"]}
            create_response = self.make_request("POST", "/projects", project_data, headers, params)
            
            if create_response.status_code in [200, 201]:
                project_response = create_response.json()
                project_id = project_response.get("id")
                self.test_data["project_id"] = project_id
                
                # Verify persistence
                get_response = self.make_request("GET", f"/projects/{project_id}", headers=headers)
                
                if get_response.status_code == 200:
                    retrieved_project = get_response.json()
                    if (retrieved_project.get("name") == project_data["name"] and 
                        retrieved_project.get("status") == project_data["status"]):
                        self.log_result("Project Persistence", "PASS", 
                                      f"Data correctly stored: {project_id}")
                    else:
                        self.log_result("Project Persistence", "FAIL", 
                                      "Retrieved data doesn't match")
                else:
                    self.log_result("Project Persistence", "FAIL", 
                                  f"Failed to retrieve: {get_response.status_code}")
            else:
                self.log_result("Project Persistence", "FAIL", 
                              f"Creation failed: {create_response.status_code}")
                
        except Exception as e:
            self.log_result("Project Persistence", "FAIL", str(e))
    
    def test_board_and_column_persistence(self):
        """Test board and column data persistence"""
        if not self.auth_token or "project_id" not in self.test_data:
            self.log_result("Board/Column Persistence", "SKIP", "Prerequisites not met")
            return
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            # Create board
            board_data = {
                "name": "Test DB Board",
                "description": "Testing board persistence"
            }
            
            params = {"project_id": self.test_data["project_id"]}
            board_response = self.make_request("POST", "/boards", board_data, headers, params)
            
            if board_response.status_code in [200, 201]:
                board_result = board_response.json()
                board_id = board_result.get("id")
                self.test_data["board_id"] = board_id
                
                # Create columns
                columns = ["To-Do", "In Progress", "Review", "Done"]
                column_ids = []
                
                for i, col_name in enumerate(columns):
                    col_data = {
                        "name": col_name,
                        "position": i,
                        "color": "#3b82f6"
                    }
                    col_params = {"board_id": board_id}
                    col_response = self.make_request("POST", "/columns", col_data, headers, col_params)
                    
                    if col_response.status_code in [200, 201]:
                        col_result = col_response.json()
                        column_ids.append(col_result.get("id"))
                
                if column_ids:
                    self.test_data["column_id"] = column_ids[0]
                    self.log_result("Board/Column Persistence", "PASS", 
                                  f"Board and {len(column_ids)} columns created")
                else:
                    self.log_result("Board/Column Persistence", "FAIL", "No columns created")
            else:
                self.log_result("Board/Column Persistence", "FAIL", 
                              f"Board creation failed: {board_response.status_code}")
                
        except Exception as e:
            self.log_result("Board/Column Persistence", "FAIL", str(e))
    
    def test_card_and_checklist_persistence(self):
        """Test card and checklist data persistence"""
        if not self.auth_token or "column_id" not in self.test_data:
            self.log_result("Card/Checklist Persistence", "SKIP", "Prerequisites not met")
            return
        
        try:
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            
            # Create card
            card_data = {
                "title": "Test DB Card",
                "description": "Testing card database persistence",
                "column_id": self.test_data["column_id"],
                "position": 0,
                "priority": "high",
                "due_date": (datetime.now() + timedelta(days=7)).isoformat()
            }
            
            card_response = self.make_request("POST", "/cards", card_data, headers)
            
            if card_response.status_code in [200, 201]:
                card_result = card_response.json()
                card_id = card_result.get("id")
                self.test_data["card_id"] = card_id
                
                # Create checklist item
                checklist_data = {
                    "text": "Test checklist item for DB persistence",
                    "position": 0
                }
                
                checklist_params = {"card_id": card_id}
                checklist_response = self.make_request("POST", "/checklist/items", 
                                                     checklist_data, headers, checklist_params)
                
                if checklist_response.status_code in [200, 201]:
                    # Verify card retrieval with checklist
                    get_response = self.make_request("GET", f"/cards/{card_id}", headers=headers)
                    
                    if get_response.status_code == 200:
                        retrieved_card = get_response.json()
                        if (retrieved_card.get("title") == card_data["title"] and 
                            retrieved_card.get("priority") == card_data["priority"]):
                            self.log_result("Card/Checklist Persistence", "PASS", 
                                          f"Card and checklist data stored: {card_id}")
                        else:
                            self.log_result("Card/Checklist Persistence", "FAIL", 
                                          "Retrieved card data doesn't match")
                    else:
                        self.log_result("Card/Checklist Persistence", "FAIL", 
                                      f"Card retrieval failed: {get_response.status_code}")
                else:
                    self.log_result("Card/Checklist Persistence", "PARTIAL", 
                                  f"Card created but checklist failed: {checklist_response.status_code}")
            else:
                self.log_result("Card/Checklist Persistence", "FAIL", 
                              f"Card creation failed: {card_response.status_code}")
                
        except Exception as e:
            self.log_result("Card/Checklist Persistence", "FAIL", str(e))
    
    def run_all_tests(self):
        """Run all database verification tests"""
        print("🗄️ Starting Database Verification Tests")
        print("=" * 50)
        
        # Setup authentication
        if not self.setup_authentication():
            print("❌ Authentication setup failed. Cannot proceed with tests.")
            return
        
        # Run persistence tests
        self.test_organization_persistence()
        self.test_project_persistence()
        self.test_board_and_column_persistence()
        self.test_card_and_checklist_persistence()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 50)
        print("📊 DATABASE VERIFICATION SUMMARY")
        print("=" * 50)
        
        passed = len([r for r in self.test_results if r["status"] == "PASS"])
        failed = len([r for r in self.test_results if r["status"] == "FAIL"])
        skipped = len([r for r in self.test_results if r["status"] == "SKIP"])
        partial = len([r for r in self.test_results if r["status"] == "PARTIAL"])
        
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"⚠️  Skipped: {skipped}")
        print(f"🔶 Partial: {partial}")
        print(f"📈 Total: {len(self.test_results)}")
        
        if failed > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if result["status"] == "FAIL":
                    print(f"   - {result['test']}: {result['details']}")

if __name__ == "__main__":
    tester = DatabaseVerificationTest()
    tester.run_all_tests()
