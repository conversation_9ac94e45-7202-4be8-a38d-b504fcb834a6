#!/usr/bin/env python3
"""
Test script to verify all endpoints are working correctly
"""
import asyncio
import asyncpg
import requests
import time
import json
from production_server import app
import uvicorn
import threading

# Test configuration
TEST_BASE_URL = "http://localhost:3001"
DATABASE_URL = "postgresql://postgres:admin@localhost:5432/agno_worksphere"

def start_server():
    """Start the production server in a separate thread"""
    uvicorn.run(app, host="0.0.0.0", port=3001, log_level="info")

def test_endpoints():
    """Test all the endpoints we added"""
    print("🚀 Testing Backend Endpoints")
    print("=" * 60)
    
    # Wait for server to start
    time.sleep(3)
    
    endpoints_to_test = [
        ("GET", "/health", "Health Check"),
        ("GET", "/api/v1/users/me", "Current User"),
        ("GET", "/api/v1/organizations", "Organizations"),
        ("GET", "/api/v1/projects", "Projects"),
        ("GET", "/api/v1/boards", "Boards"),
        ("GET", "/api/v1/columns", "Columns"),
        ("GET", "/api/v1/cards", "Cards"),
    ]
    
    results = []
    
    for method, endpoint, name in endpoints_to_test:
        try:
            url = f"{TEST_BASE_URL}{endpoint}"
            if method == "GET":
                response = requests.get(url, timeout=5)
            elif method == "POST":
                response = requests.post(url, json={}, timeout=5)
            
            status = "✅ PASS" if response.status_code in [200, 201] else "❌ FAIL"
            print(f"{status} {name} ({endpoint})")
            print(f"    📝 Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'data' in data:
                        print(f"    📊 Data: {type(data['data']).__name__}")
                    elif 'success' in data:
                        print(f"    📊 Success: {data['success']}")
                except:
                    print(f"    📊 Response length: {len(response.text)}")
            else:
                print(f"    📊 Error: {response.text[:100]}")
            
            results.append((name, response.status_code == 200))
            
        except Exception as e:
            print(f"❌ FAIL {name} ({endpoint})")
            print(f"    📝 Error: {str(e)}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    print(f"📈 Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All endpoints are working correctly!")
    else:
        print(f"\n⚠️ {total - passed} endpoints need attention.")

async def test_database_data():
    """Test that data is properly stored in the database"""
    print("\n🗄️ Testing Database Data Storage")
    print("=" * 60)
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Test tables exist and have data
        tables_to_check = [
            ("users", "Users"),
            ("organizations", "Organizations"), 
            ("projects", "Projects"),
            ("boards", "Boards"),
            ("columns", "Columns"),
            ("cards", "Cards"),
            ("card_assignments", "Card Assignments"),
            ("checklist_items", "Checklist Items")
        ]
        
        for table, name in tables_to_check:
            try:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                print(f"✅ {name}: {count} records")
            except Exception as e:
                print(f"❌ {name}: Error - {str(e)}")
        
        # Test specific data integrity
        print("\n📋 Data Integrity Checks")
        print("-" * 30)
        
        # Check cards have proper relationships
        cards_with_columns = await conn.fetchval("""
            SELECT COUNT(*) FROM cards c 
            JOIN columns col ON c.column_id = col.id
        """)
        print(f"✅ Cards with valid columns: {cards_with_columns}")
        
        # Check card assignments
        assignments = await conn.fetchval("""
            SELECT COUNT(*) FROM card_assignments ca
            JOIN cards c ON ca.card_id = c.id
            JOIN users u ON ca.user_id = u.id
        """)
        print(f"✅ Valid card assignments: {assignments}")
        
        # Check checklist items
        checklist_count = await conn.fetchval("""
            SELECT COUNT(*) FROM checklist_items ci
            JOIN cards c ON ci.card_id = c.id
        """)
        print(f"✅ Checklist items: {checklist_count}")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ Database connection error: {str(e)}")

def main():
    """Main test function"""
    print("🔧 Starting Backend Endpoint Tests")
    print("=" * 60)
    
    # Start server in background thread
    server_thread = threading.Thread(target=start_server, daemon=True)
    server_thread.start()
    
    # Test endpoints
    test_endpoints()
    
    # Test database
    asyncio.run(test_database_data())
    
    print("\n✅ Testing completed!")

if __name__ == "__main__":
    main()
