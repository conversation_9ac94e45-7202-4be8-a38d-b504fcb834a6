#!/usr/bin/env python3
"""
Fix the users table ID column to have proper auto-increment sequence
"""
import asyncio
import asyncpg

async def fix_user_id_sequence():
    """Fix the users table ID column to auto-increment"""
    print("🔧 Fixing Users Table ID Sequence...")
    
    try:
        # Connect to database
        conn = await asyncpg.connect("postgresql://postgres:admin@localhost:5432/agno_worksphere")
        print("✅ Connected to PostgreSQL database")
        
        # Check current table structure
        print("🔍 Checking current users table structure...")
        
        table_info = await conn.fetch("""
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'users' AND table_schema = 'public'
            ORDER BY ordinal_position
        """)
        
        print("📋 Current users table structure:")
        for col in table_info:
            print(f"  • {col['column_name']}: {col['data_type']} (default: {col['column_default']}, nullable: {col['is_nullable']})")
        
        # Check if ID column has a sequence
        id_column = next((col for col in table_info if col['column_name'] == 'id'), None)
        
        if id_column and 'nextval' not in str(id_column['column_default'] or ''):
            print("🔧 ID column doesn't have auto-increment. Creating sequence...")
            
            # Get the current maximum ID
            max_id = await conn.fetchval("SELECT COALESCE(MAX(id), 0) FROM users")
            print(f"📊 Current maximum ID: {max_id}")
            
            # Create sequence starting from max_id + 1
            await conn.execute(f"""
                CREATE SEQUENCE IF NOT EXISTS users_id_seq 
                START WITH {max_id + 1} 
                INCREMENT BY 1 
                OWNED BY users.id
            """)
            print("✅ Created users_id_seq sequence")
            
            # Set the default value for the ID column
            await conn.execute("""
                ALTER TABLE users 
                ALTER COLUMN id SET DEFAULT nextval('users_id_seq')
            """)
            print("✅ Set ID column default to use sequence")
            
            # Update the sequence ownership
            await conn.execute("ALTER SEQUENCE users_id_seq OWNED BY users.id")
            print("✅ Set sequence ownership")
            
        else:
            print("✅ ID column already has proper auto-increment setup")
        
        # Verify the fix
        print("\n🔍 Verifying the fix...")
        updated_info = await conn.fetchrow("""
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'id'
        """)
        
        if updated_info:
            print(f"✅ ID column verification: {updated_info['column_name']} - Type: {updated_info['data_type']}, Default: {updated_info['column_default']}")
        
        await conn.close()
        print("✅ Database ID sequence fix completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database ID sequence fix failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_user_id_sequence())
    if success:
        print("\n🎉 Users table ID sequence is now properly configured!")
    else:
        print("\n💥 Users table ID sequence fix failed!")
